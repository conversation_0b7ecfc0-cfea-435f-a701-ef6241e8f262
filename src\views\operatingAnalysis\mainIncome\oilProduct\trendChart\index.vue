<template>
  <div class="trend-chart">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  name: "trendChart",
  data() {
    return {
      xData: ["1月", "2月", "3月", "4月", "5月", "6月"],
      y1Data: [148, 152, 171, 154, 201, 193],
      y2Data: [136, 156, 193, 106, 201, 193],
      myChart: null, // 存储图表实例
      tooltipTimer: null, // 防抖定时器
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  beforeDestroy() {
    // 组件销毁时清理资源
    if (this.tooltipTimer) {
      clearTimeout(this.tooltipTimer);
    }
    if (this.myChart) {
      this.myChart.dispose();
    }
  },
  methods: {
    initChart() {
      // 如果已有实例，先销毁
      if (this.myChart) {
        this.myChart.dispose();
      }

      this.myChart = echarts.init(this.$refs.chartBox);
      this.option = {
        color: ["#248EFF", "#7262FD"], //圆柱体颜色
        tooltip: {
          trigger: "axis", // 改为axis触发，更适合对比数据
          confine: true, // 限制tooltip在图表区域内
          borderWidth: 0,
          backgroundColor: "rgba(12, 15, 41, 0.9)",
          extraCssText: "box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.5);border-radius: 8px;z-index: 9999;",
          textStyle: {
            fontFamily: "Source Han Sans",
            color: "#FFFFFF",
            fontSize: 14,
          },
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          },
          formatter: this.tooltipFormatter,
        },
        grid: {
          top: "14%",
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: this.xData,
            axisTick: {
              alignWithLabel: true,
            },
            nameTextStyle: {
              color: "#ACC2E2", //文本颜色
            },
            axisLine: {
              lineStyle: {
                color: "rgba(172, 194, 226, 0.2)", //轴线颜色
              },
            },
            axisLabel: {
              textStyle: {
                color: "#ACC2E2", //轴线文本颜色
              },
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "亿方",
            position: "left",
            nameTextStyle: {
              color: "#ACC2E2",
              align: "right",
              padding: [0, 10, 0, 0],
            },
            axisLabel: {
              textStyle: {
                color: "#ACC2E2",
              },
              formatter: "{value}",
            },
            splitLine: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: "产量",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [-12, -5],
            symbolPosition: "end",
            z: 12,
            label: {
              normal: {
                show: false,
              },
            },
            data: this.y1Data,
          },
          {
            name: "产量",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [-12, 5],
            z: 12,
            data: this.y1Data,
          },
          {
            name: "产量",
            type: "bar",
            itemStyle: {
              normal: {
                opacity: 0.7,
              },
            },
            barWidth: "20",
            data: this.y1Data,
          },
          {
            name: "同期",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [12, -5],
            symbolPosition: "end",
            z: 12,
            label: {
              normal: {
                show: false,
              },
            },
            data: this.y2Data,
          },
          {
            name: "同期",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [12, 5],
            z: 12,
            data: this.y2Data,
          },
          {
            name: "同期",
            type: "bar",
            itemStyle: {
              normal: {
                opacity: 0.7,
              },
            },
            barWidth: "20",
            data: this.y2Data,
          },
          // {
          //   name: "崖城13-1",
          //   type: "line",
          //   data: [45, 52, 88, 42, 95],
          //   itemStyle: {
          //     color: "#FF6660", // 设置线条颜色为红色
          //   },
          // },
          // {
          //   name: "崖城13-10",
          //   type: "line",
          //   data: [30, 33, 40, 42, 55],
          //   itemStyle: {
          //     color: "#F7AE44", // 设置线条颜色为红色
          //   },
          // },
        ],
      };
      this.myChart.setOption(this.option);

      // 添加鼠标事件监听，实现防抖优化
      this.addChartEventListeners();
    },

    // Tooltip格式化函数
    tooltipFormatter(params) {
      if (!params || params.length === 0) return '';

      const axisValue = params[0].axisValue;
      let tooltipContent = `<div style="margin-bottom: 8px; font-weight: bold;">${axisValue}</div>`;

      // 按系列名称分组数据
      const seriesMap = new Map();
      params.forEach(param => {
        const seriesName = param.seriesName;
        if (!seriesMap.has(seriesName)) {
          seriesMap.set(seriesName, []);
        }
        seriesMap.get(seriesName).push(param);
      });

      // 生成tooltip内容
      seriesMap.forEach((seriesParams, seriesName) => {
        if (seriesParams.length > 0) {
          const param = seriesParams[0]; // 取第一个参数作为代表
          const value = param.value;
          const color = param.color;

          tooltipContent += `
            <div style="display: flex; align-items: center; margin-bottom: 4px;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${color}; border-radius: 50%; margin-right: 8px;"></span>
              <span style="margin-right: 8px;">${seriesName}:</span>
              <span style="font-weight: bold;">${value} 亿方</span>
            </div>
          `;
        }
      });

      return tooltipContent;
    },

    // 添加图表事件监听器，实现性能优化
    addChartEventListeners() {
      if (!this.myChart) return;

      // 鼠标移入事件 - 使用防抖优化
      this.myChart.on('mouseover', this.debounce((params) => {
        // 可以在这里添加额外的鼠标悬停逻辑
        console.log('Chart mouseover:', params);
      }, 100));

      // 鼠标移出事件
      this.myChart.on('mouseout', () => {
        // 清理防抖定时器
        if (this.tooltipTimer) {
          clearTimeout(this.tooltipTimer);
        }
      });
    },

    // 防抖函数，优化鼠标事件性能
    debounce(func, wait) {
      return (...args) => {
        if (this.tooltipTimer) {
          clearTimeout(this.tooltipTimer);
        }
        this.tooltipTimer = setTimeout(() => {
          func.apply(this, args);
        }, wait);
      };
    },

    // 图表重绘方法（可用于响应数据变化）
    updateChart() {
      if (this.myChart && this.option) {
        // 更新数据
        this.option.xAxis[0].data = this.xData;
        this.option.series.forEach((series) => {
          if (series.name === '产量') {
            series.data = this.y1Data;
          } else if (series.name === '同期') {
            series.data = this.y2Data;
          }
        });

        // 重新设置配置，不合并
        this.myChart.setOption(this.option, true);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.trend-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .chart-box {
    width: 100%;
    flex: 1;
    min-height: 200px; // 减少最小高度，适应容器空间
    max-height: 280px; // 添加最大高度限制
  }
}
</style>
