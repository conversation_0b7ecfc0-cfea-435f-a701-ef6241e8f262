import { instance } from "../config";

// 预算执行
export function getBudgetExecution(data) {
  return instance({
    url: `/cockpit/costOverview?date=${data}`,
    method: "post",
  });
}

// 方油/方气处理费
export function getProcessFeeBox(data) {
  return instance({
    url: `/cockpit/handlingFee?date=${data}`,
    method: "post",
  });
}

// 获取重点关注药剂
export function getFocusMedicine(data) {
  return instance({
    url: `/cockpit/focusList?hzNo=${data}`,
    method: "post",
  });
}

// 获取重点关注图
export function getFocusMedicineChart(query) {
  return instance({
    url: `/cockpit/focusLineChart`,
    method: "post",
    params: query,
  });
}

// 年度药剂加注
export function getDrugAddition(data) {
  return instance({
    url: `/cockpit/yearInject?date=${data}`,
    method: "post",
  });
}

// 药剂服务/分析化验
export function getMedicineService(data) {
  return instance({
    url: `/cockpit/chemicalServe?date=${data}`,
    method: "post",
  });
}
