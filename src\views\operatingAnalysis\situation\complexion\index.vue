<template>
  <div class="swiper-box">
    <div class="buttonBox">
      <div
        class="buttonItem"
        :class="{ active: activeIndex === index }"
        @click="clickHandle(index)"
        v-for="(item, index) in buttons"
        :key="index"
      >
        {{ item }}
      </div>
    </div>

    <div class="stage">
      <div
        v-for="(tab, index) in tabs"
        :key="index"
        class="card"
        :class="getCardClass(index)"
      >
        <div class="title">{{ tab.label }}</div>
        <div class="card-content">
          <chart-box />
        </div>
      </div>
    </div>

    <div class="card-bottom"></div>
  </div>
</template>
<script>
import ChartBox from "./ChartBox.vue";
export default {
  name: "buttonItem",
  components: { ChartBox },
  props: {},
  data() {
    return {
      activeIndex: 0,
      buttons: ["节能量", "减排量", "劳动生产量"],
      tabs: [
        {
          label: "节能量",
          content: "节能量相关内容展示在此处，可替换为图表或表格",
        },
        { label: "减排量", content: "减排量的趋势图或说明内容" },
        { label: "劳动生产量", content: "人均产值、效率等数据展示" },
      ],
    };
  },
  methods: {
    clickHandle(index) {
      this.activeIndex = index;
      this.$emit("clickHandle", index);
    },
    getCardClass(index) {
      const total = this.tabs.length;
      const relativeIndex = (index - this.activeIndex + total) % total;

      switch (relativeIndex) {
        case 0:
          return "center";
        case 1:
          return "right";
        case 2:
          return "left";
        default:
          return "";
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.swiper-box {
  position: relative;
  width: 100%;
  height: 88%;
  margin: auto;
  color: #fff;

  .buttonBox {
    margin-left: 12px;
    display: flex;
    .buttonItem {
      margin-right: 8px;
      width: 128px;
      height: 35px;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 16px;
      font-weight: 400;
    }
  }

  .stage {
    position: relative;
    height: 100%;
    perspective: 1200px;

    .card {
      position: absolute;
      width: 70%;
      height: 328px;
      transition: all 0.5s ease;
      color: #fff;

      .title {
        display: flex;
        justify-content: center;
        line-height: 40px;
        height: 48px;
        background-size: 100% 100%;

        font-family: Source Han Sans;
        font-size: 16px;
        font-weight: bold;
      }
    }
  }
  .card-bottom {
    margin-top: 20px;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 50%;
    height: 60px;
    background-size: 100% 100%;
  }
}

.buttonItem:hover {
  color: #fff !important;
}

[data-theme="dark"] .buttonItem {
  background-image: url("@/assets/tableicon/sbutton-darkbg.png");
  color: #6ba4f4;
}

[data-theme="dark"] .active {
  background-image: url("@/assets/tableicon/sbuttonactive-darkbg.png") !important;
  color: #fff !important;
}

[data-theme="dark"] .card {
  background: #131f41;
  .title {
    background-image: url("@/assets/tableicon/stitle-darkbg.png");
  }
}

[data-theme="dark"] .card-bottom {
  background-image: url("@/assets/tableicon/sbase-darkbg.png");
}

[data-theme="tint"] .buttonItem {
  background-image: url("@/assets/tableicon/sbutton-tintbg.png");
}

[data-theme="tint"] .active {
  background-image: url("@/assets/tableicon/sbuttonactive-tintbg.png") !important;
  color: #fff !important;
}
[data-theme="tint"] .card-bottom {
  background-image: url("@/assets/tableicon/sbase-tintbg.png");
}

/* 位置样式 */
.card.center {
  left: 15%;
  top: 50px;
  z-index: 10;
  transform: scale(1);
  opacity: 1;
}
.card.left {
  left: 0;
  top: 36px;
  z-index: 5;
  transform: scale(0.85) rotateY(15deg);
  opacity: 0.8;
}
.card.right {
  left: 30%;
  top: 36px;
  z-index: 5;
  transform: scale(0.85) rotateY(-15deg);
  opacity: 0.8;
}
.card.back {
  left: 250px;
  top: 40px;
  z-index: 1;
  transform: scale(0.75);
  opacity: 0.6;
}
</style>
