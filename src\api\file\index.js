import { instance } from "../config";
// 获取文件
export function getFileList(ids) {
  return instance({
    url: `/attachment/getInfoByIds?ids=${ids}`,
    method: "get",
  });
}

// 下载文件
export function downloadFileById(id) {
  return instance({
    url: `/attachment/downloadFile?id=${id}`,
    method: "get",
    responseType: "blob",
  })
}


// 预览文件
export function previewFileById(id) {
  return instance({
    url: `/attachment/getPreSignedObjectUrlById?id=${id}`,
    method: "get",
  })
}