<template>
    <div class="cfbgc-echart">
        <cf-spin v-show="loading"/>
        <div class="container" ref="container">

        </div>
    </div>
</template>
<script>
import * as echarts from 'echarts';

export default {
    name: 'cfbgcEchart',
    props: {
        loading: {
            type: Boolean,
            default() {
                return false
            }
        },
        options: {
            graphic: {
                elements: [
                    {
                        type: 'group',
                        left: 'center',
                        top: 'center',
                        children: new Array(7).fill(0).map((val, i) => ({
                            type: 'rect',
                            x: i * 20,
                            shape: {
                                x: 0,
                                y: -40,
                                width: 10,
                                height: 80
                            },
                            style: {
                                fill: '#5470c6'
                            },
                            keyframeAnimation: {
                                duration: 1000,
                                delay: i * 200,
                                loop: true,
                                keyframes: [
                                    {
                                        percent: 0.5,
                                        scaleY: 0.3,
                                        easing: 'cubicIn'
                                    },
                                    {
                                        percent: 1,
                                        scaleY: 1,
                                        easing: 'cubicOut'
                                    }
                                ]
                            }
                        }))
                    }
                ]
            }
        }
    },
    watch: {
        options: {
            handler(){
                this.setOption()
            },
            deep: true
        }
    },
    data() {
        return {
            echartClient: null
        }
    },
    mounted() {
        this.$nextTick()
            .then(() => {
                this.init()
            })
    },
    methods: {
        init() {
            this.echartClient = echarts.init(this.$refs.container);
            this.setOption()
        },
        setOption() {
            this.echartClient.clear()
            this.echartClient.setOption(this.options)
        }
    }

}
</script>
<style lang="scss">
.cfbgc-echart {
    height: 20rem;
    width: 20rem;
    position: relative;

    .container {
        width: 100%;
        height: 100%;
    }

    .ant-spin-spinning {
        position: absolute;
        z-index: 6;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        background: rgba(11,25, 56, 0.48);
    }
}
</style>
