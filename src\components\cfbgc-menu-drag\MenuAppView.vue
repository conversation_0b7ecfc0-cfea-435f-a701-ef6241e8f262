<template>
    <div class="menuComponent">
        <cf-menu
            style="width: 100%"
            :open-keys.sync="openKeys"
            mode="inline"
            :inlineIndent="0"
            :inlineCollapsed="false"
            @click="handleClick"
            theme="dark"
        >
            <cf-sub-menu
                v-for="v in menuList"
                :key="v.name"
                @titleClick="titleClick"
            >
                <span slot="title">
                    <img class="icon_LevelOne" src="../assets/icon/icon_LevelOne.png" alt="">
                    <span v-if="v.meta">
                        {{ v.meta.title }}
                    </span>
                </span>
                <cf-menu-item v-for="value in v.children" :key="value.name" @click="routerFun(v,value)">
                    <img class="icon_LevelTwo" src="../assets/icon/icon_LevelTwo.png" alt=""/>
                    <span v-if="value.meta">
                        {{ value.meta.title }}
                    </span>
                </cf-menu-item>
            </cf-sub-menu>
        </cf-menu>
    </div>
</template>

<script>
export default {
    name: '<PERSON>uA<PERSON>View',
    props: {
        menuList: {
            type: Array,
        },
    },
    data() {
        return {
            openKeys: [],
        };
    },
    watch: {
        openKeys(val) {
            console.log("openKeys", val);
        },
    },
    computed: {},
    methods: {
        handleClick(e) {
            console.log("click", e);
        },
        titleClick(e) {
            console.log("titleClick", e);
        },
        routerFun(v, e) {
            this.$router.push(v.path + e.path)
        }
    },
    created() {
        // console.log(this.menuList, 123213);
    },
    mounted() {
    },
};
</script>
<style lang="scss">
.menuComponent {
    user-select: none;

    .ant-menu-inline .ant-menu-submenu {
        padding-bottom: 0;
    }

    .ant-menu-submenu-arrow {
        display: none;
    }

    .ant-menu-inline .ant-menu-submenu-title {
        font-size: 0.1397rem;
        color: white;
        background-image: linear-gradient(to right, #4983c5, #2b4d7b);
        border-bottom: 0.0466rem solid #587fb5;
        margin: 0px;
        height: 0.9315rem;
    }

    .ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title {
        height: 1.6302rem;
        line-height: 1.6302rem;
    }

    .ant-menu-sub.ant-menu-inline > .ant-menu-item {
        margin: 0rem;
        color: white;
        background-color: #1d3358;
        border-bottom: 1px solid #2b4d7b;
        height: 1.6302rem;
        line-height: 1.6302rem;
    }

    .ant-menu-inline .ant-menu-item::after {
        border-right: 0rem;
    }

    .ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
        background-color: #d59758;
    }

    .ant-menu-submenu-inline
    & > .ant-menu-submenu-title
    .ant-menu-submenu-arrow::after {
        display: none;
    }

    .ant-menu-submenu-inline
    > .ant-menu-submenu-title
    .ant-menu-submenu-arrow::before {
        display: none;
    }

    .menu_logo {
        width: 0.9315rem;
    }

    .svg {
        font-size: .6986rem;
        font-weight: 600;
        color: #36bdde;
    }

    .icon_LevelOne {
        width: 20px;
        height: 15px;
    }

    .icon_LevelTwo {
        width: .27rem;
        height: .3rem;
        margin-left: .9315rem;
    }
}

</style>
