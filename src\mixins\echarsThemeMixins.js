import { chartsTint, chartsDark } from '@/utils/tools.js'
export default {
    data() {
        return {
            tint: chartsTint,
            dark: chartsDark,
            themeColor: {},
            theme: '',
            observer: null,
        }
    },
    computed: {
        curTheme() {
            return this.$store.state.curTheme;
        },
    },
    watch: {
        curTheme: {
            handler(newValue, oldValue) {
                if (newValue) {
                    console.log('当前主题为：暗黑');
                    this.themeColor = this.dark
                } else {
                    console.log('当前主题为：纯白');
                    this.themeColor = this.tint
                }
                if (this.setOPtions) {
                    this.setOPtions(this.themeColor)
                }
            },
            immediate: true // 页面初始化时立即执行一次
        }
    }
    // watch: {
    //     theme(val) {
    //         this.themeColor = this[val];
    //     },
    // },
}