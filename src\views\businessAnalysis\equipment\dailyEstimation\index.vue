<template>
  <div class="dailyEstimation-container">
    <div class="dailyEstimation-header">
      <HeaderItem></HeaderItem>
      <HeaderItem class="item-mid"></HeaderItem>
      <HeaderItem></HeaderItem>
    </div>
    <div class="dailyEstimation-chart">
      <div class="chart" ref="chart"></div>
    </div>
  </div>
</template>
<script>
import * as echarts from "echarts";
import BarChart from "./bar/barChart.vue";
import HeaderItem from "./HeaderItem.vue";
export default {
  components: { BarChart, HeaderItem },
  data() {
    return {
      mychart: null,
      xData: ["2025-06-01", "2025-06-02", "2025-06-03", "2025-06-04", "2025-06-05", "2025-06-06", "2025-06-07"],
      yData: [120, 200, 150, 80, 70, 110, 130],
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  methods: {
    initChart() {
      const dom = this.$refs.chart;
      if(dom && dom.clientWidth && dom.clientHeight) {
      let mychart = echarts.init(this.$refs.chart);
      this.option = {
        backgroundColor: "#162549",
        color: ["#248EFF"], //圆柱体颜色
        tooltip: {
          trigger: "item",
          padding: 1,
          formatter: function (param) {},
        },
        grid: {
          left: "32px",
          top: "48px",
        },
        xAxis: [
          {
            type: "category",
            data: this.xData,
            axisTick: {
              alignWithLabel: true,
            },
            nameTextStyle: {
              color: "#ACC2E2", //文本颜色
            },
            axisLine: {
              lineStyle: {
                color: "rgba(172, 194, 226, 0.2)", //轴线颜色
              },
            },
            axisLabel: {
              textStyle: {
                color: "#ACC2E2", //轴线文本颜色
              },
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "万元",
            position: "left",
            nameTextStyle: {
              color: "#ACC2E2",
              align: "right",
            },
            axisLabel: {
              textStyle: {
                color: "#ACC2E2",
              },
              formatter: "{value}",
            },
            splitLine: {
              lineStyle: {
                color: "#0c2c5a",
              },
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "rgba(172, 194, 226, 0.2)",
              },
            },
          },
        ],
        series: [
          {
            name: "",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [0, -5],
            symbolPosition: "end",
            z: 12,
            label: {
              normal: {
                show: false,
              },
            },
            data: this.yData,
          },
          {
            name: "",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [0, 5],
            z: 12,
            data: this.yData,
          },
          {
            type: "bar",
            itemStyle: {
              normal: {
                opacity: 0.7,
              },
            },
            barWidth: "20",
            data: this.yData,
          },
        ],
      };
      mychart.setOption(this.option);
      }else {
        setTimeout(() => {
          this.initChart();
        },300)
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.dailyEstimation-container {
  box-sizing: border-box;
  padding: 16px;
  max-width: 100%;
  max-height: 100%;
  .dailyEstimation-header {
    display: flex;
    justify-content: space-around;
    .item-mid {
      margin: 0 16px;
    }
  }

  .dailyEstimation-chart {
    min-height: 280px;
    box-sizing: border-box;
    max-width: 100%;
    flex: 1;
    .chart {
      z-index: 1;
      width: 100%;
      height: 280px;
    }
  }
}
</style>