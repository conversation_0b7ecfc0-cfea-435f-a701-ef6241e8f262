import { instance } from "../config";

// 化学药剂使用
export const getTestChemicals = (data) => {
  return instance({
    url:"/ChemicalAnalysis/list",
    method:"post",
    data
  })
}

export const saveTestChemical = (data) => {
    return instance({
      url:"/ChemicalAnalysis/save",
      method:"post",
      data
    })
}

export const addTestChemical = (data) => {
    return instance({
      url:"/ChemicalAnalysis/submit",
      method:"post",
      data
    })
}

export const deleteTestChemical = (params) => {
    return instance({
      url:"/ChemicalAnalysis/delete",
      method:"post",
      params
    })
}