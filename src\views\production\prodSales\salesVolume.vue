<template>
  <div class="sales-volume">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  name: "SalesVolume",
  data() {
    return {
      xData: [],
      yData: [],
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  methods: {
    initChart() {
      let mychart = echarts.init(this.$refs.chartBox);
      this.option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          data: ["YC13-1", "YC13-10","LS17-2","LS25-1"], //当前装置
          right: "right",
          top:"center"
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        series: [
          {
            name: "Access From",
            type: "pie",
            radius: ["40%", "70%"],
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            data: [
              { value: 1048, name: "YC13-1" },
              { value: 735, name: "YC13-10" },
              { value: 484, name: "LS17-2" },
              { value: 300, name: "LS25-1" },
            ],
          },
        ],
      };
      mychart.setOption(this.option);
    },
  },
};
</script>
<style lang="scss" scoped>
.sales-volume {
  width: 100%;
  .chart-box {
    width: 90%;
    height: 320px;
  }
}
</style>
