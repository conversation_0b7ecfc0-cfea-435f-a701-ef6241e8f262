<template>
  <div class="search-bar">
    <el-form :inline="true" :model="formData" class="form-body">
      <el-form-item
        v-for="(field, index) in visibleFields"
        :key="field.prop"
        :label="field.label"
        :class="[
          { 'month-range': field.type === 'monthrange' },
          { 'date-range': field.type === 'daterange' },
          { month: field.type === 'month' },
          { year: field.type === 'year' },
        ]"
      >
        <!-- Select -->
        <el-select
          :popper-append-to-body="false"
          v-if="field.type === 'select'"
          v-model="formData[field.prop]"
          :placeholder="field.placeholder || '请选择'"
          clearable
        >
          <el-option
            v-for="opt in (field.options || []).filter(Boolean)"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
          />
        </el-select>

        <!-- Input with debounce -->
        <el-input
          v-else-if="field.type === 'input'"
          v-model="inputBuffer[field.prop]"
          :placeholder="field.placeholder || '请输入'"
          clearable
          @input="debounceInput(field.prop)"
        />

        <!-- Date:year -->
        <el-date-picker
          :append-to-body="false"
          v-else-if="field.type === 'year'"
          v-model="formData[field.prop]"
          type="year"
          :placeholder="field.placeholder || '选择年份'"
          format="yyyy年"
          value-format="yyyy"
          clearable
        />

        <!-- Date: month -->
        <el-date-picker
          :append-to-body="false"
          v-else-if="field.type === 'month'"
          v-model="formData[field.prop]"
          type="month"
          :placeholder="field.placeholder || '选择月份'"
          format="yyyy年MM月"
          value-format="yyyy-MM"
          clearable
        />

        <!-- Date range -->
        <el-date-picker
          :append-to-body="false"
          v-else-if="field.type === 'daterange'"
          v-model="formData[field.prop]"
          type="daterange"
          :start-placeholder="field.startPlaceholder || '开始日期'"
          :end-placeholder="field.endPlaceholder || '结束日期'"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          clearable
        />

        <div v-else-if="field.type === 'monthrange'">
          <el-date-picker
            :append-to-body="false"
            v-model="formData[field.prop][0]"
            type="month"
            :placeholder="field.startPlaceholder || '开始月份'"
            format="yyyy年MM月"
            value-format="yyyy-MM"
            clearable
            style="width: 140px"
          />
          <span style="margin: 0 5px">至</span>
          <el-date-picker
            :append-to-body="false"
            v-model="formData[field.prop][1]"
            type="month"
            :placeholder="field.endPlaceholder || '结束月份'"
            format="yyyy年MM月"
            value-format="yyyy-MM"
            clearable
            style="width: 140px"
          />
        </div>
      </el-form-item>

      <!-- 按钮 -->
      <el-form-item class="btns">
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
        <slot name="actions" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "SearchBar",
  props: {
    fields: {
      type: Array,
      required: true,
    },
    value: {
      type: Object,
      default: () => ({}),
    },
    baseVisibleCount: {
      type: Number,
      default: 3, // 默认展示前 N 项
    },
    debounceTime: {
      type: Number,
      default: 400, // ms
    },
  },
  data() {
    const formData = { ...this.value };
    // 确保所有字段都有初始值
    this.fields.forEach((field) => {
      if (!(field.prop in formData)) {
        formData[field.prop] =
          field.type === "daterange" || field.type === "monthrange" ? [] : "";
      }
    });

    return {
      formData,
      inputBuffer: { ...formData },
      debounceTimers: {},
    };
  },
  computed: {
    visibleFields() {
      return this.fields.filter((f) => f.visible !== false);
    },
  },
  watch: {
    value: {
      handler(val) {
        // 只更新实际变化的字段
        Object.keys(val).forEach((key) => {
          if (JSON.stringify(this.formData[key]) !== JSON.stringify(val[key])) {
            this.$set(this.formData, key, val[key]);
          }
        });
        this.inputBuffer = { ...this.formData };
      },
      immediate: true,
      deep: true,
    },
    formData: {
      handler(val) {
        this.$emit("input", { ...val });
      },
      deep: true,
    },
  },
  methods: {
    handleSearch() {
      this.$emit("search", { ...this.formData });
    },
    handleReset() {
      const cleared = {};
      this.visibleFields.forEach(
        (f) => (cleared[f.prop] = f.type === "daterange" || f.type === "monthrange" ? [] : "")
      );
      this.formData = cleared;
      this.inputBuffer = { ...cleared };
      this.$emit("reset", { ...this.formData });
    },
    debounceInput(prop) {
      if (this.debounceTimers[prop]) clearTimeout(this.debounceTimers[prop]);
      this.debounceTimers[prop] = setTimeout(() => {
        this.formData[prop] = this.inputBuffer[prop];
      }, this.debounceTime);
    },
  },
};
</script>

<style lang="scss" scoped>
.search-bar {
  margin: 20px 0;
  padding-right: 0;
}

.btns {
  width: 260px !important;
}

::v-deep .month-range {
  width: 320px !important;
  .el-form-item__content {
    width: 100% !important;
  }
}

::v-deep .year {
  .el-date-editor.el-input {
    width: 100% !important;
  }
}

::v-deep .month {
  width: 270px !important;
}

// 浅色主题样式
[data-theme="default"],
[data-theme="tint"] {
  .search-bar {
    // 输入框样式
    ::v-deep .el-input__inner {
      background-color: #fff !important;
      border-color: #e2e2e2 !important;
      color: #000 !important;
    }

    // 表单标签颜色
    ::v-deep .el-form-item__label {
      color: #606266 !important;
    }

    // 下拉选择器样式
    ::v-deep .el-select-dropdown {
      background-color: #fff !important;
      border-color: #e2e2e2 !important;
    }

    ::v-deep .el-select-dropdown__item {
      color: #606266 !important;

      &:hover {
        background-color: #f5f7fa !important;
      }

      &.selected {
        background-color: #409eff !important;
        color: #fff !important;
      }

      &.is-disabled {
        color: #c0c4cc !important;
      }
    }

    // 下拉箭头
    ::v-deep .el-select .el-input .el-select__caret {
      color: #c0c4cc !important;

      &:hover {
        color: #909399 !important;
      }
    }

    // 选择器标签
    ::v-deep .el-select .el-select__tags {
      .el-tag {
        background-color: #f0f2f5 !important;
        border-color: #d9d9d9 !important;
        color: #606266 !important;

        .el-tag__close {
          color: #909399 !important;

          &:hover {
            background-color: #c0c4cc !important;
            color: #fff !important;
          }
        }
      }
    }

    // 日期选择器样式
    ::v-deep .el-picker-panel {
      background-color: #fff !important;
      border-color: #e2e2e2 !important;
    }

    ::v-deep .el-date-table th {
      color: #606266 !important;
    }

    ::v-deep .el-date-table td {
      color: #606266 !important;

      &.available:hover {
        background-color: #f2f6fc !important;
      }

      &.current:not(.disabled) {
        background-color: #409eff !important;
        color: #fff !important;
      }
    }

    ::v-deep .el-picker-panel__icon-btn {
      color: #606266 !important;

      &:hover {
        color: #409eff !important;
      }
    }

    ::v-deep .el-picker-panel__shortcut {
      color: #606266 !important;

      &:hover {
        color: #409eff !important;
      }
    }

    ::v-deep .el-year-table td {
      color: #606266 !important;

      &.available:hover {
        background-color: #f2f6fc !important;
      }

      &.current:not(.disabled) {
        background-color: #409eff !important;
        color: #fff !important;
      }
    }

    ::v-deep .el-month-table td {
      color: #606266 !important;

      &.available:hover {
        background-color: #f2f6fc !important;
      }

      &.current:not(.disabled) {
        background-color: #409eff !important;
        color: #fff !important;
      }
    }

    // 按钮样式
    ::v-deep .el-button {
      &:not(.el-button--primary) {
        background-color: #fff !important;
        border-color: #dcdfe6 !important;
        color: #606266 !important;

        &:hover {
          background-color: #ecf5ff !important;
          border-color: #b3d8ff !important;
          color: #409eff !important;
        }
      }
    }

    // 占位符颜色
    ::v-deep .el-input__inner::placeholder {
      color: #c0c4cc !important;
    }

    // 清除按钮
    ::v-deep .el-input__suffix {
      .el-input__icon {
        color: #c0c4cc !important;

        &:hover {
          color: #909399 !important;
        }
      }
    }

    // 月份范围选择器中间文字
    .month-range span {
      color: #606266;
    }
  }
}

// 深色主题样式
[data-theme="dark"] {
  .search-bar {
    // 输入框样式
    ::v-deep .el-input__inner {
      background-color: #1A2E52 !important;
      border-color: #4F98F6 !important;
      color: #ffffff !important;
    }

    // 表单标签颜色
    ::v-deep .el-form-item__label {
      color: #CCE4FF !important;
    }

    // 下拉选择器样式
    ::v-deep .el-select-dropdown {
      background-color: #1A2E52 !important;
      border-color: #4F98F6 !important;
    }

    ::v-deep .el-select-dropdown__item {
      color: #CCE4FF !important;

      &:hover {
        background-color: #254489 !important;
      }

      &.selected {
        background-color: #4EA0FC !important;
        color: #ffffff !important;
      }

      &.is-disabled {
        color: #6493D4 !important;
      }
    }

    // 下拉箭头
    ::v-deep .el-select .el-input .el-select__caret {
      color: #6493D4 !important;

      &:hover {
        color: #CCE4FF !important;
      }
    }

    // 选择器标签
    ::v-deep .el-select .el-select__tags {
      .el-tag {
        background-color: #254489 !important;
        border-color: #4F98F6 !important;
        color: #CCE4FF !important;

        .el-tag__close {
          color: #CCE4FF !important;

          &:hover {
            background-color: #4EA0FC !important;
            color: #ffffff !important;
          }
        }
      }
    }

    // 日期选择器样式
    ::v-deep .el-picker-panel {
      background-color: #1A2E52 !important;
      border-color: #4F98F6 !important;
    }

    ::v-deep .el-date-table th {
      color: #CCE4FF !important;
    }

    ::v-deep .el-date-table td {
      color: #CCE4FF !important;

      // 可用日期悬停状态
      &.available:hover {
        background-color: #254489 !important;
        color: #ffffff !important;
      }

      // 当前选中日期 - 确保高对比度
      &.current:not(.disabled) {
        background-color: #4EA0FC !important;
        color: #ffffff !important;
        font-weight: bold !important;
        &.cell{
          color: #ffffff !important;
        }
      }

      // 今天日期
      &.today {
        color: #4EA0FC !important;
        font-weight: bold !important;
      }

      // 今天且被选中的日期
      &.today.current:not(.disabled) {
        background-color: #4EA0FC !important;
        color: #ffffff !important;
        font-weight: bold !important;
        &.cell{
          color: #ffffff !important;
        }
      }

      // 禁用日期
      &.disabled {
        color: #6493D4 !important;
        background-color: transparent !important;
      }

      // 其他月份的日期
      &.prev-month,
      &.next-month {
        color: #6493D4 !important;
      }

      // 其他月份日期悬停
      &.prev-month:hover,
      &.next-month:hover {
        background-color: #254489 !important;
        color: #CCE4FF !important;
      }
    }

    ::v-deep .el-picker-panel__icon-btn {
      color: #CCE4FF !important;

      &:hover {
        color: #4EA0FC !important;
      }
    }

    ::v-deep .el-picker-panel__shortcut {
      color: #CCE4FF !important;

      &:hover {
        color: #4EA0FC !important;
      }
    }

    // 日期范围选择器特殊样式
    ::v-deep .el-date-table td {
      // 范围选择中的日期
      &.in-range {
        background-color: rgba(78, 160, 252, 0.2) !important;
        color: #CCE4FF !important;
      }

      // 范围开始日期
      &.start-date {
        background-color: #4EA0FC !important;
        color: #ffffff !important;
        font-weight: bold !important;
      }

      // 范围结束日期
      &.end-date {
        background-color: #4EA0FC !important;
        color: #ffffff !important;
        font-weight: bold !important;
      }

      // 范围选择悬停效果
      &.in-range:hover {
        background-color: rgba(78, 160, 252, 0.3) !important;
        color: #ffffff !important;
      }
    }

    // 时间选择器样式（如果有）
    ::v-deep .el-time-panel {
      background-color: #1A2E52 !important;
      border-color: #4F98F6 !important;
    }

    ::v-deep .el-time-panel__content {
      background-color: #1A2E52 !important;
    }

    ::v-deep .el-scrollbar__wrap {
      background-color: #1A2E52 !important;
    }

    ::v-deep .el-time-spinner__item {
      color: #CCE4FF !important;

      &:hover {
        background-color: #254489 !important;
        color: #ffffff !important;
      }

      &.active {
        background-color: #4EA0FC !important;
        color: #ffffff !important;
        font-weight: bold !important;
      }
    }

    // 日期选择器头部样式
    ::v-deep .el-picker-panel__header {
      color: #CCE4FF !important;
    }

    ::v-deep .el-picker-panel__header-label {
      color: #CCE4FF !important;

      &:hover {
        color: #4EA0FC !important;
      }
    }

    ::v-deep .el-year-table td {
      color: #CCE4FF !important;

      // 年份悬停状态
      &.available:hover {
        background-color: #254489 !important;
        color: #ffffff !important;
      }

      // 当前选中年份 - 确保高对比度
      &.current:not(.disabled) {
        background-color: #4EA0FC !important;
        color: #ffffff !important;
        font-weight: bold !important;
      }

      // 今年
      &.today {
        color: #4EA0FC !important;
        font-weight: bold !important;
      }

      // 今年且被选中
      &.today.current:not(.disabled) {
        background-color: #4EA0FC !important;
        color: #ffffff !important;
        font-weight: bold !important;
      }

      // 禁用年份
      &.disabled {
        color: #6493D4 !important;
        background-color: transparent !important;
      }
    }

    ::v-deep .el-month-table td {
      color: #CCE4FF !important;

      // 月份悬停状态
      &.available:hover {
        background-color: #254489 !important;
        color: #ffffff !important;
      }

      // 当前选中月份 - 确保高对比度
      &.current:not(.disabled) {
        background-color: #4EA0FC !important;
        color: #ffffff !important;
        font-weight: bold !important;
      }

      // 当前月份
      &.today {
        color: #4EA0FC !important;
        font-weight: bold !important;
      }

      // 当前月份且被选中
      &.today.current:not(.disabled) {
        background-color: #4EA0FC !important;
        color: #ffffff !important;
        font-weight: bold !important;
      }

      // 禁用月份
      &.disabled {
        color: #6493D4 !important;
        background-color: transparent !important;
      }
    }

    // 按钮样式
    ::v-deep .el-button {
      &:not(.el-button--primary) {
        background-color: #1A2E52 !important;
        border-color: #4F98F6 !important;
        color: #CCE4FF !important;

        &:hover {
          background-color: #254489 !important;
          border-color: #4EA0FC !important;
          color: #ffffff !important;
        }
      }
    }

    // 占位符颜色
    ::v-deep .el-input__inner::placeholder {
      color: #6493D4 !important;
    }

    // 清除按钮
    ::v-deep .el-input__suffix {
      .el-input__icon {
        color: #6493D4 !important;

        &:hover {
          color: #CCE4FF !important;
        }
      }
    }

    // 月份范围选择器中间文字
    .month-range span {
      color: #CCE4FF;
    }

    // 强制修复日期选择器可读性问题 - 使用更高优先级的选择器
    ::v-deep .el-picker-panel .el-month-table td.available.current {
      background-color: #4EA0FC !important;
      color: #ffffff !important;
      font-weight: bold !important;
    }

    ::v-deep .el-picker-panel .el-date-table td.available.current {
      background-color: #4EA0FC !important;
      color: #ffffff !important;
      font-weight: bold !important;
    }

    ::v-deep .el-picker-panel .el-year-table td.available.current {
      background-color: #4EA0FC !important;
      color: #ffffff !important;
      font-weight: bold !important;
    }

    // 修复选中状态的文字颜色 - 最高优先级
    ::v-deep .el-picker-panel .el-month-table td.current span,
    ::v-deep .el-picker-panel .el-date-table td.current span,
    ::v-deep .el-picker-panel .el-year-table td.current span {
      color: #ffffff !important;
      font-weight: bold !important;
    }

    // 修复悬停状态的文字颜色
    ::v-deep .el-picker-panel .el-month-table td:hover span,
    ::v-deep .el-picker-panel .el-date-table td:hover span,
    ::v-deep .el-picker-panel .el-year-table td:hover span {
      color: #ffffff !important;
    }

    // 确保所有日期选择器面板的背景色
    ::v-deep .el-picker-panel,
    ::v-deep .el-picker-panel__body,
    ::v-deep .el-picker-panel__content {
      background-color: #1A2E52 !important;
      border-color: #4F98F6 !important;
    }

    // 额外的强制修复 - 针对Element UI的具体类名
    ::v-deep .el-month-table td.current,
    ::v-deep .el-month-table td.current:hover,
    ::v-deep .el-date-table td.current,
    ::v-deep .el-date-table td.current:hover,
    ::v-deep .el-year-table td.current,
    ::v-deep .el-year-table td.current:hover {
      background-color: #4EA0FC !important;
      color: #ffffff !important;
      font-weight: bold !important;
    }

    // 针对月份表格的特殊修复 - 最高优先级
    ::v-deep .el-month-table {
      td {
        color: #CCE4FF !important;

        &.current {
          background-color: #4EA0FC !important;
          color: #ffffff !important;
          font-weight: bold !important;

          // 强制修复内部文字颜色
          span, div, .cell {
            color: #ffffff !important;
            font-weight: bold !important;
          }
        }

        &:hover:not(.current) {
          background-color: #254489 !important;
          color: #ffffff !important;
        }

        &.today {
          color: #4EA0FC !important;
          font-weight: bold !important;
        }

        &.today.current {
          background-color: #4EA0FC !important;
          color: #ffffff !important;
          font-weight: bold !important;

          // 强制修复内部文字颜色
          span, div, .cell {
            color: #ffffff !important;
            font-weight: bold !important;
          }
        }
      }
    }

    // 超级强制修复 - 针对所有可能的月份选择器元素
    ::v-deep .el-picker-panel__body .el-month-table td.current,
    ::v-deep .el-picker-panel__body .el-month-table td.current:hover,
    ::v-deep .el-picker-panel__body .el-month-table td.current:focus,
    ::v-deep .el-picker-panel__body .el-month-table td.current:active {
      background-color: #4EA0FC !important;
      color: #ffffff !important;
      font-weight: bold !important;

      // 强制修复所有内部元素
      *, span, div, .cell, a {
        color: #ffffff !important;
        font-weight: bold !important;
      }
    }

    // 针对日期表格的特殊修复
    ::v-deep .el-date-table {
      td {
        color: #CCE4FF !important;

        &.current {
          background-color: #4EA0FC !important;
          color: #ffffff !important;
          font-weight: bold !important;

          // 强制修复内部文字颜色
          span, div, .cell {
            color: #ffffff !important;
            font-weight: bold !important;
          }
        }

        &:hover:not(.current) {
          background-color: #254489 !important;
          color: #ffffff !important;
        }

        &.today {
          color: #4EA0FC !important;
          font-weight: bold !important;
        }

        &.today.current {
          background-color: #4EA0FC !important;
          color: #ffffff !important;
          font-weight: bold !important;
        }
      }
    }

    // 针对年份表格的特殊修复
    ::v-deep .el-year-table {
      td {
        color: #CCE4FF !important;

        &.current {
          background-color: #4EA0FC !important;
          color: #ffffff !important;
          font-weight: bold !important;
        }

        &:hover:not(.current) {
          background-color: #254489 !important;
          color: #ffffff !important;
        }

        &.today {
          color: #4EA0FC !important;
          font-weight: bold !important;
        }

        &.today.current {
          background-color: #4EA0FC !important;
          color: #ffffff !important;
          font-weight: bold !important;
        }
      }
    }

    // ========== 最终强制修复方案 ==========
    // 使用最高优先级选择器强制修复月份选择器可读性问题

    // 方案1: 直接针对Element UI的类名
    ::v-deep .el-month-table td.current,
    ::v-deep .el-month-table td.current:hover,
    ::v-deep .el-month-table td.current:focus,
    ::v-deep .el-month-table td.current:active,
    ::v-deep .el-month-table td.current:visited {
      background-color: #4EA0FC !important;
      color: #ffffff !important;
      font-weight: bold !important;
    }

    // 方案2: 针对所有可能的内部元素
    ::v-deep .el-month-table td.current *,
    ::v-deep .el-month-table td.current span,
    ::v-deep .el-month-table td.current div,
    ::v-deep .el-month-table td.current a,
    ::v-deep .el-month-table td.current .cell {
      color: #ffffff !important;
      font-weight: bold !important;
    }

    // 方案3: 使用属性选择器
    ::v-deep .el-month-table td[class*="current"] {
      background-color: #4EA0FC !important;
      color: #ffffff !important;
      font-weight: bold !important;

      * {
        color: #ffffff !important;
        font-weight: bold !important;
      }
    }

    // 方案4: 使用更具体的选择器路径
    ::v-deep .el-picker-panel .el-picker-panel__body .el-month-table td.current {
      background-color: #4EA0FC !important;
      color: #ffffff !important;
      font-weight: bold !important;

      span, div, a, .cell, * {
        color: #ffffff !important;
        font-weight: bold !important;
      }
    }

    // 方案5: 针对日期选择器的强制修复
    ::v-deep .el-date-table td.current,
    ::v-deep .el-year-table td.current {
      background-color: #4EA0FC !important;
      color: #ffffff !important;
      font-weight: bold !important;

      *, span, div, a, .cell {
        color: #ffffff !important;
        font-weight: bold !important;
      }
    }

    // 方案6: 使用CSS变量覆盖（如果Element UI使用了CSS变量）
    ::v-deep .el-picker-panel {
      --el-color-primary: #4EA0FC;
      --el-text-color-primary: #ffffff;
      --el-text-color-regular: #CCE4FF;
    }

    // 方案7: 最后的杀手锏 - 使用内联样式级别的优先级
    ::v-deep .el-month-table td.current {
      background: #4EA0FC !important;
      color: #ffffff !important;
      font-weight: bold !important;
    }

    ::v-deep .el-month-table td.current span {
      color: #ffffff !important;
      font-weight: bold !important;
    }
  }
}

::v-deep .el-form-item {
  width: 240px;
  .el-form-item__content {
    width: 60%;
  }
}
</style>
