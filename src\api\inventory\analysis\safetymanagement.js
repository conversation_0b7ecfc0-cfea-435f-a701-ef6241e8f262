import {instance} from "../../config";
import qs from 'qs'

// 安全库存列表查询
export function sapInfoSafety_list(data) {
    return instance({
        url: '/im-ls/sapInfoSafety/list',
        method: "POST",
        data
    });
}

// 查看安全库存
export function sapInfoSafety_detail(params) {
    return instance({
        url: '/im-ls/sapInfoSafety',
        method: "GET",
        params
    });
}

// 新增/编辑安全库存
export function sapInfoSafety_add_edit(data) {
    return instance({
        url: '/im-ls/sapInfoSafety',
        method: "POST",
        data
    });
}

// 根据物料编号判断是否存在安全库存
export function sapInfoSafety_checkExist(params) {
    return instance({
        url: '/im-ls/sapInfoSafety/checkExist',
        method: "GET",
        params
    });
}
// 批量删除安全库存
export function sapInfoSafety_del(params) {
    return instance({
        url: `/im-ls/sapInfoSafety/remove?` + qs.stringify({ ids: params }, { indices: false }),
        method: "POST",
        params
    });
}

//安全库存 模板下载
export function sapInfoSafety_download() {
    return instance({
        url: '/im-ls/sapInfoSafety/download',
        method: "POST",
        responseType: "blob",
    });
}


// 安全库存导入
export function sapInfoSafety_export(data) {
    return instance({
        url: '/im-ls/sapInfoSafety/export',
        method: "POST",
        responseType: "blob",
        data
    });
}

// 安全库存导出
export function sapInfoSafety_import(data) {
    return instance({
        url: '/im-ls/sapInfoSafety/import',
        method: "POST",
        responseType: "blob",
        data
    });
}


/*根据物料编码获取已有的安全库存信息*/
export function sapInfoSafety_ori(params) {
    return instance({
        url: '/im-ls/sapInfoSafety/ori',
        method: "GET",
        params
    });
}

/*查看安全库存操作日志*/
export function sapInfoSafety_log(params) {
    return instance({
        url: '/im-ls/sapInfoSafety/log',
        method: "GET",
        params
    });
}
