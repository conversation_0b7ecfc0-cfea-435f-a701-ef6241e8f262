<template>
  <div class="chart-container">
    <div class="chart" ref="chart"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
import { getOption } from "./data";
export default {
  name: "linesChart",
  props: {
    legendData: {
      type: Array,
      default: () => [],
    },
    xData: {
      type: Array,
      default: () => [],
    },
    yData: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    yData: {
      handler() {
        this.initChart();
      },
      deep: true,
    },
  },
  data() {
    return {

    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  methods: {
    initChart() {
      const chart = echarts.init(this.$refs.chart);
      chart.setOption(getOption(this.xData, this.yData, this.legendData));
    },
  },
};
</script>
<style lang="scss" scoped>
.chart-container {
  width: 100%;
  height: 100%;
  flex: 1;
  .chart {
    z-index: 1;
  }
  .chart {
    width: 100%;
    height: 100%;
  }
}
</style>
