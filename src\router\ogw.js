
const changeRouter = {
  path: "/ogw",
  nameCN: "油气水管理",
  component: {
    render: (h) => h("keep-alive", {}, [h("router-view")]),
  },
  children: [
    {
      path: "costStatistics",
      name: "costStatistics",
      nameCN: "费用执行统计",
      component: () =>
        import("@/views/businessAnalysis/ogwWatch/costStatistics/index.vue"),
      meta: {
        personalized: "",
      },
    },
    {
      path: "medicineConfig",
      name: "medicineConfig",
      nameCN: "药剂配置",
      component: () =>
        import("@/views/businessAnalysis/ogwWatch/medicineConfig/index.vue"),
      meta: {
        personalized: "",
      },
    },
    {
      path: "ogwActiveRecord",
      name: "ogwActiveRecord",
      nameCN: "药剂记录",
      component: () =>
        import("@/views/businessAnalysis/ogwWatch/ogwActivityRecord/index.vue"),
      meta: {
        personalized: "",
      },
      children: [
        {
          path: "useChemical",
          name: "useChemical",
          component: () =>
            import(
              "@/views/businessAnalysis/ogwWatch/ogwActivityRecord/useChemicals/index.vue"
            ),
        },
        {
          path: "filterChemical",
          name: "filterChemical",
          component: () =>
            import(
              "@/views/businessAnalysis/ogwWatch/ogwActivityRecord/filterChemicals/index.vue"
            ),
        },
        {
          path: "evaluateChemical",
          name: "evaluateChemical",
          component: () =>
            import(
              "@/views/businessAnalysis/ogwWatch/ogwActivityRecord/evaluationChemicals/index.vue"
            ),
        },
        {
          path: "testChemical",
          name: "testChemical",
          component: () =>
            import(
              "@/views/businessAnalysis/ogwWatch/ogwActivityRecord/testChemicals/index.vue"
            ),
        },
      ],
    },
    {
      path: "/ogw/chemicalForm",
      name: "chemicalForm",
      component: () =>
        import(
          "@/views/businessAnalysis/ogwWatch/ogwActivityRecord/preview/applicationForm.vue"
        ),
    },
    {
      path: "/ogw/opinionsPre",
      name: "opinionsPre",
      component: () =>
        import(
          "@/views/businessAnalysis/ogwWatch/ogwActivityRecord/preview/reportAOpinions.vue"
        ),
    },
    {
      path: "/ogw/radio",
      name: "radio",
      component: () =>
        import(
          "@/views/businessAnalysis/ogwWatch/ogwActivityRecord/useChemicals/radio/index.vue"
        ),
    },
  ],
};

export default changeRouter;
