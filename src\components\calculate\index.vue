<!-- 智能气藏配产 -->
<template>
  <div class="main">
    <div class="left">
      <div class="title">风载荷</div>
      <div class="item">
        <div class="label">风向</div>
        <el-input class="value" v-model="fengxiang"></el-input>
        deg
      </div>
      <div class="item">
        <div class="label">风速</div>
        <el-input class="value" v-model="fengsu"></el-input>
        m/s
      </div>
      <div class="title" style="margin-top;: 30px">波浪载荷</div>
      <div class="item">
        <div class="label">波浪方向</div>
        <el-input class="value" v-model="blfx"></el-input>
        deg
      </div>
      <div class="item">
        <div class="label">波浪高度</div>
        <el-input class="value" v-model="blgd"></el-input>
        m
      </div>
      <div class="item">
        <div class="label">波浪周期</div>
        <el-input class="value" v-model="blzq"></el-input>
        s
      </div>
      <div class="title" style="margin-top: 30px">海流载荷</div>
      <div class="item">
        <div class="label">海流流向</div>
        <el-input class="value" v-model="hllx"></el-input>
        deg
      </div>
      <div class="item">
        <div class="label">表层流速</div>
        <el-input class="value" v-model="bcls"></el-input>
        m/s
      </div>
      <div class="item">
        <div class="label">底层流速</div>
        <el-input class="value" v-model="dcls"></el-input>
        m/s
      </div>
      <div class="item">
        <div class="label">时间步长</div>
        <el-input class="value" v-model="sjbc"></el-input>
        m/s
      </div>
      <div class="line"></div>
      <div class="progress">
        <div class="text">
          <div>计算</div>
          <div>50%</div>
        </div>
        <el-progress
          :percentage="50"
          :stroke-width="5"
          :show-text="false"
        ></el-progress>
      </div>
      <el-button class="btn" type="primary" @click="jisuan" size="mini"
        >开始计算</el-button
      >
    </div>
    <div class="right">
      <el-table :data="tableData" border style="width: 100%">
        <el-table-column
          prop="type"
          label="锚链编号"
          align="center"
        ></el-table-column>
        <el-table-column
          v-for="item in config"
          :key="item.prop"
          width="68"
          :prop="item.prop"
          :label="item.label"
          align="center"
        ></el-table-column>
      </el-table>
      <div class="chartBox">
        <div style="width: 100%; height: 100%" id="chart1"></div>
      </div>
      <div class="svgBox">
        <div class="svgLeft">
          <div class="chartPart">
            <div class="partItem">
              <!-- <img class="chartImg" src="@/assets/images/mooring/total2.svg" > -->
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 360 200">
                <g
                  id="组_710"
                  data-name="组 710"
                  transform="translate(-8220.806 2264)"
                >
                  <g
                    id="组_704"
                    data-name="组 704"
                    transform="translate(8312.768 -2245.734)"
                  >
                    <g
                      id="组_699"
                      data-name="组 699"
                      transform="translate(48.723 47.011)"
                    >
                      <g
                        id="组_646"
                        data-name="组 646"
                        transform="translate(0 1.597)"
                      >
                        <path
                          id="路径_226"
                          data-name="路径 226"
                          d="M958.265,539.871h-66.4a1.608,1.608,0,0,1-1.606-1.607v-66.4a1.608,1.608,0,0,1,1.606-1.606h66.4a1.608,1.608,0,0,1,1.607,1.606v66.4A1.608,1.608,0,0,1,958.265,539.871Zm-66.4-68.606a.6.6,0,0,0-.6.6v66.4a.6.6,0,0,0,.6.6h66.4a.6.6,0,0,0,.6-.6v-66.4a.6.6,0,0,0-.6-.6Z"
                          transform="translate(-890.261 -470.261)"
                          fill="#fff"
                        />
                      </g>
                      <g
                        id="组_647"
                        data-name="组 647"
                        transform="translate(11.406 13.003)"
                      >
                        <path
                          id="路径_227"
                          data-name="路径 227"
                          d="M954.9,539.769H917.838a4.871,4.871,0,0,1-4.866-4.866V497.838a4.871,4.871,0,0,1,4.866-4.866H954.9a4.871,4.871,0,0,1,4.866,4.866V534.9A4.871,4.871,0,0,1,954.9,539.769Zm-37.066-46.295a4.369,4.369,0,0,0-4.364,4.364V534.9a4.368,4.368,0,0,0,4.364,4.363H954.9a4.368,4.368,0,0,0,4.364-4.363V497.838a4.369,4.369,0,0,0-4.364-4.364Z"
                          transform="translate(-912.972 -492.972)"
                          fill="#fff"
                        />
                      </g>
                      <g
                        id="组_650"
                        data-name="组 650"
                        transform="translate(0.502 2.099)"
                      >
                        <g
                          id="组_648"
                          data-name="组 648"
                          transform="translate(0 12.236)"
                        >
                          <rect
                            id="矩形_259"
                            data-name="矩形 259"
                            width="12.417"
                            height="0.502"
                            fill="#fff"
                          />
                        </g>
                        <g
                          id="组_649"
                          data-name="组 649"
                          transform="translate(12.166)"
                        >
                          <rect
                            id="矩形_260"
                            data-name="矩形 260"
                            width="0.502"
                            height="12.487"
                            fill="#fff"
                          />
                        </g>
                      </g>
                      <g
                        id="组_653"
                        data-name="组 653"
                        transform="translate(0.502 57.967)"
                      >
                        <g id="组_651" data-name="组 651">
                          <rect
                            id="矩形_261"
                            data-name="矩形 261"
                            width="12.417"
                            height="0.502"
                            fill="#fff"
                          />
                        </g>
                        <g
                          id="组_652"
                          data-name="组 652"
                          transform="translate(12.166 0.251)"
                        >
                          <rect
                            id="矩形_262"
                            data-name="矩形 262"
                            width="0.502"
                            height="12.487"
                            fill="#fff"
                          />
                        </g>
                      </g>
                      <g
                        id="组_656"
                        data-name="组 656"
                        transform="translate(57.001 57.967)"
                      >
                        <g
                          id="组_654"
                          data-name="组 654"
                          transform="translate(0.251)"
                        >
                          <rect
                            id="矩形_263"
                            data-name="矩形 263"
                            width="12.417"
                            height="0.502"
                            fill="#fff"
                          />
                        </g>
                        <g
                          id="组_655"
                          data-name="组 655"
                          transform="translate(0 0.251)"
                        >
                          <rect
                            id="矩形_264"
                            data-name="矩形 264"
                            width="0.502"
                            height="12.487"
                            fill="#fff"
                          />
                        </g>
                      </g>
                      <g
                        id="组_659"
                        data-name="组 659"
                        transform="translate(56.44 2.099)"
                      >
                        <g
                          id="组_657"
                          data-name="组 657"
                          transform="translate(0.251 12.236)"
                        >
                          <rect
                            id="矩形_265"
                            data-name="矩形 265"
                            width="12.417"
                            height="0.502"
                            fill="#fff"
                          />
                        </g>
                        <g id="组_658" data-name="组 658">
                          <rect
                            id="矩形_266"
                            data-name="矩形 266"
                            width="0.502"
                            height="12.487"
                            fill="#fff"
                          />
                        </g>
                      </g>
                      <g
                        id="组_660"
                        data-name="组 660"
                        transform="translate(22.638 11.111)"
                      >
                        <path
                          id="路径_228"
                          data-name="路径 228"
                          d="M935.8,491.443l-.465-.189.835-2.049H960.5l.54,2.08-.487.126-.442-1.7h-23.6Z"
                          transform="translate(-935.335 -489.205)"
                          fill="#fff"
                        />
                      </g>
                      <g
                        id="组_661"
                        data-name="组 661"
                        transform="translate(22.638 2.437)"
                      >
                        <path
                          id="路径_229"
                          data-name="路径 229"
                          d="M960.5,474.173h-24.33l-.835-2.049.465-.189.707,1.736h23.6l.442-1.7.487.126Z"
                          transform="translate(-935.335 -471.935)"
                          fill="#fff"
                        />
                      </g>
                      <g
                        id="组_662"
                        data-name="组 662"
                        transform="translate(22.638 68.128)"
                      >
                        <path
                          id="路径_230"
                          data-name="路径 230"
                          d="M935.8,604.968l-.465-.189.835-2.049H960.5l.54,2.08-.487.127-.442-1.7h-23.6Z"
                          transform="translate(-935.335 -602.729)"
                          fill="#fff"
                        />
                      </g>
                      <g
                        id="组_663"
                        data-name="组 663"
                        transform="translate(22.638 59.455)"
                      >
                        <path
                          id="路径_231"
                          data-name="路径 231"
                          d="M960.5,587.7h-24.33l-.835-2.048.465-.189.707,1.735h23.6l.442-1.7.487.127Z"
                          transform="translate(-935.335 -585.46)"
                          fill="#fff"
                        />
                      </g>
                      <g
                        id="组_664"
                        data-name="组 664"
                        transform="translate(9.514 23.544)"
                      >
                        <path
                          id="路径_232"
                          data-name="路径 232"
                          d="M911.253,539.664l-2.049-.834V514.5l2.08-.539.126.486-1.7.442v23.6l1.736.707Z"
                          transform="translate(-909.205 -513.96)"
                          fill="#fff"
                        />
                      </g>
                      <g
                        id="组_665"
                        data-name="组 665"
                        transform="translate(0.84 23.544)"
                      >
                        <path
                          id="路径_233"
                          data-name="路径 233"
                          d="M892.124,539.664l-.189-.465,1.736-.707v-23.6l-1.7-.442.126-.486,2.08.539V538.83Z"
                          transform="translate(-891.935 -513.96)"
                          fill="#fff"
                        />
                      </g>
                      <g
                        id="组_666"
                        data-name="组 666"
                        transform="translate(66.35 23.544)"
                      >
                        <path
                          id="路径_234"
                          data-name="路径 234"
                          d="M1024.416,539.664l-2.049-.834V514.5l2.081-.539.126.486-1.7.442v23.6l1.736.707Z"
                          transform="translate(-1022.367 -513.96)"
                          fill="#fff"
                        />
                      </g>
                      <g
                        id="组_667"
                        data-name="组 667"
                        transform="translate(57.676 23.544)"
                      >
                        <path
                          id="路径_235"
                          data-name="路径 235"
                          d="M1005.287,539.664l-.189-.465,1.735-.707v-23.6l-1.7-.442.126-.486,2.08.539V538.83Z"
                          transform="translate(-1005.098 -513.96)"
                          fill="#fff"
                        />
                      </g>
                      <g
                        id="组_668"
                        data-name="组 668"
                        transform="translate(3.724 4.764)"
                      >
                        <path
                          id="矩形_267"
                          data-name="矩形 267"
                          d="M-.5-.5H5.195V6.825H-.5ZM4.594.1H.1V6.224H4.594Z"
                          transform="translate(0.5 0.5)"
                          fill="#fff"
                        />
                      </g>
                      <g
                        id="组_669"
                        data-name="组 669"
                        transform="translate(3.724 60.598)"
                      >
                        <path
                          id="矩形_268"
                          data-name="矩形 268"
                          d="M-.5-.5H5.195V6.825H-.5ZM4.594.1H.1V6.224H4.594Z"
                          transform="translate(0.5 0.5)"
                          fill="#fff"
                        />
                      </g>
                      <g
                        id="组_670"
                        data-name="组 670"
                        transform="translate(59.761 4.764)"
                      >
                        <path
                          id="矩形_269"
                          data-name="矩形 269"
                          d="M-.5-.5H5.195V6.825H-.5ZM4.594.1H.1V6.224H4.594Z"
                          transform="translate(0.5 0.5)"
                          fill="#fff"
                        />
                      </g>
                      <g
                        id="组_671"
                        data-name="组 671"
                        transform="translate(59.761 60.598)"
                      >
                        <path
                          id="矩形_270"
                          data-name="矩形 270"
                          d="M-.5-.5H5.195V6.825H-.5ZM4.594.1H.1V6.224H4.594Z"
                          transform="translate(0.5 0.5)"
                          fill="#fff"
                        />
                      </g>
                      <g
                        id="组_676"
                        data-name="组 676"
                        transform="translate(60.493 69.945)"
                      >
                        <g
                          id="组_672"
                          data-name="组 672"
                          transform="translate(0 0.811)"
                        >
                          <path
                            id="路径_236"
                            data-name="路径 236"
                            d="M1012.576,609.835h-1.45l-.42-1.758.488-.117.328,1.372h.513l-.1-1.3.5-.037Z"
                            transform="translate(-1010.706 -607.96)"
                            fill="#fff"
                          />
                        </g>
                        <g
                          id="组_673"
                          data-name="组 673"
                          transform="translate(2.218 0.757)"
                        >
                          <path
                            id="路径_237"
                            data-name="路径 237"
                            d="M1015.937,609.82l-.815-1.551.444-.233.634,1.205.577-.138-.513-1.025.449-.225.8,1.591Z"
                            transform="translate(-1015.122 -607.853)"
                            fill="#fff"
                          />
                        </g>
                        <g
                          id="组_674"
                          data-name="组 674"
                          transform="translate(4.303 0.79)"
                        >
                          <path
                            id="路径_238"
                            data-name="路径 238"
                            d="M1020.461,609.677l-1.187-1.425.386-.322.915,1.1.524-.305-.435-.46.364-.345.872.919Z"
                            transform="translate(-1019.274 -607.919)"
                            fill="#fff"
                          />
                        </g>
                        <g
                          id="组_675"
                          data-name="组 675"
                          transform="translate(6.962 0)"
                        >
                          <path
                            id="路径_239"
                            data-name="路径 239"
                            d="M1025.365,608.259l-.8-.771.348-.361.51.491.51-.335-.655-.554.325-.383,1.167.988Z"
                            transform="translate(-1024.567 -606.346)"
                            fill="#fff"
                          />
                        </g>
                      </g>
                      <g
                        id="组_681"
                        data-name="组 681"
                        transform="translate(0.097 69.945)"
                      >
                        <g
                          id="组_677"
                          data-name="组 677"
                          transform="translate(7.296 0.811)"
                        >
                          <path
                            id="路径_240"
                            data-name="路径 240"
                            d="M906.43,609.835h-1.45l.137-1.835.5.037-.1,1.3h.513l.328-1.372.488.117Z"
                            transform="translate(-904.98 -607.96)"
                            fill="#fff"
                          />
                        </g>
                        <g
                          id="组_678"
                          data-name="组 678"
                          transform="translate(4.561 0.757)"
                        >
                          <path
                            id="路径_241"
                            data-name="路径 241"
                            d="M901.106,609.82l-1.572-.376.8-1.591.449.225-.513,1.025.577.138.633-1.205.444.233Z"
                            transform="translate(-899.534 -607.853)"
                            fill="#fff"
                          />
                        </g>
                        <g
                          id="组_679"
                          data-name="组 679"
                          transform="translate(2.237 0.79)"
                        >
                          <path
                            id="路径_242"
                            data-name="路径 242"
                            d="M896.346,609.677l-1.438-.839.872-.919.364.345-.436.46.524.306.915-1.1.386.322Z"
                            transform="translate(-894.907 -607.919)"
                            fill="#fff"
                          />
                        </g>
                        <g
                          id="组_680"
                          data-name="组 680"
                          transform="translate(0 0)"
                        >
                          <path
                            id="路径_243"
                            data-name="路径 243"
                            d="M891.859,608.259l-1.406-.926,1.167-.988.325.383-.655.554.509.335.51-.491.349.362Z"
                            transform="translate(-890.453 -606.346)"
                            fill="#fff"
                          />
                        </g>
                      </g>
                      <g
                        id="组_686"
                        data-name="组 686"
                        transform="translate(60.493 0)"
                      >
                        <g
                          id="组_682"
                          data-name="组 682"
                          transform="translate(0 0.039)"
                        >
                          <path
                            id="路径_244"
                            data-name="路径 244"
                            d="M1011.194,469.033l-.488-.117.42-1.758h1.45l-.137,1.835-.5-.037.1-1.3h-.513Z"
                            transform="translate(-1010.706 -467.158)"
                            fill="#fff"
                          />
                        </g>
                        <g
                          id="组_683"
                          data-name="组 683"
                          transform="translate(2.218)"
                        >
                          <path
                            id="路径_245"
                            data-name="路径 245"
                            d="M1016.714,469.049l-.449-.225.513-1.025-.577-.138-.634,1.205-.444-.233.815-1.551,1.572.376Z"
                            transform="translate(-1015.122 -467.082)"
                            fill="#fff"
                          />
                        </g>
                        <g
                          id="组_684"
                          data-name="组 684"
                          transform="translate(4.303 0.176)"
                        >
                          <path
                            id="路径_246"
                            data-name="路径 246"
                            d="M1021.027,469.19l-.364-.346.435-.459-.524-.306-.915,1.1-.386-.321,1.187-1.425,1.438.839Z"
                            transform="translate(-1019.274 -467.432)"
                            fill="#fff"
                          />
                        </g>
                        <g
                          id="组_685"
                          data-name="组 685"
                          transform="translate(6.962 0.81)"
                        >
                          <path
                            id="路径_247"
                            data-name="路径 247"
                            d="M1025.6,470.608l-.325-.384.655-.554-.51-.336-.51.491-.348-.361.8-.77,1.406.926Z"
                            transform="translate(-1024.567 -468.695)"
                            fill="#fff"
                          />
                        </g>
                      </g>
                      <g
                        id="组_691"
                        data-name="组 691"
                        transform="translate(0.097 0)"
                      >
                        <g
                          id="组_687"
                          data-name="组 687"
                          transform="translate(7.296 0.039)"
                        >
                          <path
                            id="路径_248"
                            data-name="路径 248"
                            d="M906.361,469.033l-.328-1.372h-.513l.1,1.3-.5.037-.137-1.835h1.45l.42,1.758Z"
                            transform="translate(-904.98 -467.158)"
                            fill="#fff"
                          />
                        </g>
                        <g
                          id="组_688"
                          data-name="组 688"
                          transform="translate(4.561)"
                        >
                          <path
                            id="路径_249"
                            data-name="路径 249"
                            d="M900.329,469.049l-.8-1.591,1.572-.376.815,1.551-.444.233-.633-1.205-.577.138.513,1.025Z"
                            transform="translate(-899.534 -467.082)"
                            fill="#fff"
                          />
                        </g>
                        <g
                          id="组_689"
                          data-name="组 689"
                          transform="translate(2.237 0.176)"
                        >
                          <path
                            id="路径_250"
                            data-name="路径 250"
                            d="M895.779,469.19l-.872-.919,1.438-.839,1.187,1.425-.386.321-.915-1.1-.524.306.436.459Z"
                            transform="translate(-894.907 -467.432)"
                            fill="#fff"
                          />
                        </g>
                        <g
                          id="组_690"
                          data-name="组 690"
                          transform="translate(0 0.81)"
                        >
                          <path
                            id="路径_251"
                            data-name="路径 251"
                            d="M891.62,470.608l-1.167-.988,1.406-.926.8.77-.349.361-.51-.491-.509.336.655.554Z"
                            transform="translate(-890.453 -468.695)"
                            fill="#fff"
                          />
                        </g>
                      </g>
                      <g
                        id="组_692"
                        data-name="组 692"
                        transform="translate(33.284 34.414)"
                      >
                        <circle
                          id="椭圆_112"
                          data-name="椭圆 112"
                          cx="1.521"
                          cy="1.521"
                          r="1.521"
                          fill="#fff"
                        />
                      </g>
                      <g
                        id="组_693"
                        data-name="组 693"
                        transform="translate(34.554 27.551)"
                      >
                        <rect
                          id="矩形_271"
                          data-name="矩形 271"
                          width="0.502"
                          height="7.242"
                          transform="translate(0 0)"
                          fill="#fff"
                        />
                      </g>
                      <g
                        id="组_694"
                        data-name="组 694"
                        transform="translate(36.069 35.863)"
                      >
                        <rect
                          id="矩形_272"
                          data-name="矩形 272"
                          width="7.242"
                          height="0.502"
                          transform="translate(0 0)"
                          fill="#fff"
                        />
                      </g>
                      <g
                        id="组_695"
                        data-name="组 695"
                        transform="translate(33.483 25.254)"
                      >
                        <path
                          id="路径_252"
                          data-name="路径 252"
                          d="M958.25,517.364l-1.322,2.289h2.643Z"
                          transform="translate(-956.929 -517.364)"
                          fill="#fff"
                        />
                      </g>
                      <g
                        id="组_696"
                        data-name="组 696"
                        transform="translate(43.363 34.793)"
                      >
                        <path
                          id="路径_253"
                          data-name="路径 253"
                          d="M978.888,537.677l-2.289-1.322V539Z"
                          transform="translate(-976.599 -536.356)"
                          fill="#fff"
                        />
                      </g>
                      <g
                        id="组_697"
                        data-name="组 697"
                        transform="translate(33.042 18.154)"
                      >
                        <path
                          id="路径_254"
                          data-name="路径 254"
                          d="M958.9,512.256a1.292,1.292,0,0,0,.55-.1.9.9,0,0,0,.363-.331,2.866,2.866,0,0,0,.289-.638l-1.574-3.92h.5l1.3,3.4h.043l1.211-3.4h.473l-1.443,3.891a5.4,5.4,0,0,1-.394.841,1.33,1.33,0,0,1-.5.484,1.646,1.646,0,0,1-.815.175Z"
                          transform="translate(-958.533 -507.265)"
                          fill="#fff"
                        />
                      </g>
                      <g
                        id="组_698"
                        data-name="组 698"
                        transform="translate(46.664 33.433)"
                      >
                        <path
                          id="路径_255"
                          data-name="路径 255"
                          d="M984.062,542.056,986,539.072l-1.8-2.88h.65l1.53,2.527h.05L988,536.192h.616l-1.825,2.856,1.909,3.008h-.658l-1.622-2.627h-.05l-1.665,2.627Z"
                          transform="translate(-984.062 -536.192)"
                          fill="#fff"
                        />
                      </g>
                    </g>
                    <g
                      id="椭圆_113"
                      data-name="椭圆 113"
                      transform="translate(0.038 -0.266)"
                      fill="none"
                      stroke="#fff"
                      stroke-width="1"
                    >
                      <circle cx="83.5" cy="83.5" r="83.5" stroke="none" />
                      <circle cx="83.5" cy="83.5" r="83" fill="none" />
                    </g>
                    <g
                      id="组_700"
                      data-name="组 700"
                      transform="translate(15.538 1.776)"
                    >
                      <line
                        id="直线_132"
                        data-name="直线 132"
                        x1="34.544"
                        y1="12.942"
                        transform="translate(0 34.083)"
                        fill="none"
                        stroke="#fff"
                        stroke-width="0.5"
                      />
                      <line
                        id="直线_133"
                        data-name="直线 133"
                        x1="24.263"
                        y1="26.329"
                        transform="translate(12.094 19.79)"
                        fill="none"
                        stroke="#fff"
                        stroke-width="0.5"
                      />
                      <line
                        id="直线_134"
                        data-name="直线 134"
                        x1="6.091"
                        y1="39.522"
                        transform="translate(32.984 6.597)"
                        fill="none"
                        stroke="#fff"
                        stroke-width="0.5"
                      />
                      <line
                        id="直线_135"
                        data-name="直线 135"
                        y1="46.119"
                        x2="14.279"
                        transform="translate(41.793 0)"
                        fill="none"
                        stroke="#fff"
                        stroke-width="0.5"
                      />
                    </g>
                  </g>
                  <text
                    id="Line_Number_12_321_"
                    data-name="Line Number 12 321°"
                    transform="translate(8271.806 -2109)"
                    fill="#fff"
                    :class="{ activeName: curLine.name == 12 }"
                    font-size="10"
                    font-family="MicrosoftYaHei, Microsoft YaHei"
                  >
                    <tspan x="-50.723" y="0">Line Number 12 321°</tspan>
                  </text>
                  <text
                    id="Line_Number_11_317_"
                    data-name="Line Number 11 317°"
                    transform="translate(8285.806 -2093)"
                    fill="#fff"
                    :class="{ activeName: curLine.name == 11 }"
                    font-size="10"
                    font-family="MicrosoftYaHei, Microsoft YaHei"
                  >
                    <tspan x="-50.723" y="0">Line Number 11 317°</tspan>
                  </text>
                  <text
                    id="Line_Number_10_313_"
                    data-name="Line Number 10 313°"
                    transform="translate(8308.806 -2076)"
                    fill="#fff"
                    :class="{ activeName: curLine.name == 10 }"
                    font-size="10"
                    font-family="MicrosoftYaHei, Microsoft YaHei"
                  >
                    <tspan x="-50.723" y="0">Line Number 10 313°</tspan>
                  </text>
                  <text
                    id="Line_Number_9_309_"
                    data-name="Line Number 9 309°"
                    transform="translate(8340.806 -2063)"
                    fill="#fff"
                    :class="{ activeName: curLine.name == 9 }"
                    font-size="10"
                    font-family="MicrosoftYaHei, Microsoft YaHei"
                  >
                    <tspan x="-47.791" y="0">Line Number 9 309°</tspan>
                  </text>
                  <text
                    id="Line_Number_15_317_"
                    data-name="Line Number 15 317°"
                    transform="translate(8311.806 -2240)"
                    fill="#fff"
                    :class="{ activeName: curLine.name == 15 }"
                    font-size="10"
                    font-family="MicrosoftYaHei, Microsoft YaHei"
                  >
                    <tspan x="-50.723" y="0">Line Number 15 317°</tspan>
                  </text>
                  <text
                    id="Line_Number_15_317_2"
                    data-name="Line Number 15 317°"
                    transform="translate(8342.806 -2252)"
                    fill="#fff"
                    :class="{ activeName: curLine.name == 16 }"
                    font-size="10"
                    font-family="MicrosoftYaHei, Microsoft YaHei"
                  >
                    <tspan x="-50.723" y="0">Line Number 16 321°</tspan>
                  </text>
                  <text
                    id="Line_Number_1_39_"
                    data-name="Line Number 1 39°"
                    transform="translate(8449.806 -2253)"
                    fill="#fff"
                    :class="{ activeName: curLine.name == 1 }"
                    font-size="10"
                    font-family="MicrosoftYaHei, Microsoft YaHei"
                  >
                    <tspan x="-44.858" y="0">Line Number 1 39°</tspan>
                  </text>
                  <text
                    id="Line_Number_2_43_"
                    data-name="Line Number 2 43°"
                    transform="translate(8479.806 -2240)"
                    fill="#fff"
                    :class="{ activeName: curLine.name == 2 }"
                    font-size="10"
                    font-family="MicrosoftYaHei, Microsoft YaHei"
                  >
                    <tspan x="-44.858" y="0">Line Number 2 43°</tspan>
                  </text>
                  <text
                    id="Line_Number_3_47_"
                    data-name="Line Number 3 47°"
                    transform="translate(8500.806 -2225)"
                    fill="#fff"
                    :class="{ activeName: curLine.name == 3 }"
                    font-size="10"
                    font-family="MicrosoftYaHei, Microsoft YaHei"
                  >
                    <tspan x="-44.858" y="0">Line Number 3 47°</tspan>
                  </text>
                  <text
                    id="Line_Number_4_51_"
                    data-name="Line Number 4 51°"
                    transform="translate(8515.806 -2209)"
                    fill="#fff"
                    :class="{ activeName: curLine.name == 4 }"
                    font-size="10"
                    font-family="MicrosoftYaHei, Microsoft YaHei"
                  >
                    <tspan x="-44.858" y="0">Line Number 4 51°</tspan>
                  </text>
                  <text
                    id="Line_Number_8_141_"
                    data-name="Line Number 8 141°"
                    transform="translate(8451.806 -2065)"
                    fill="#fff"
                    :class="{ activeName: curLine.name == 8 }"
                    font-size="10"
                    font-family="MicrosoftYaHei, Microsoft YaHei"
                  >
                    <tspan x="-47.791" y="0">Line Number 8 141°</tspan>
                  </text>
                  <text
                    id="Line_Number_7_137_"
                    data-name="Line Number 7 137°"
                    transform="translate(8482.806 -2079)"
                    fill="#fff"
                    :class="{ activeName: curLine.name == 7 }"
                    font-size="10"
                    font-family="MicrosoftYaHei, Microsoft YaHei"
                  >
                    <tspan x="-47.791" y="0">Line Number 7 137°</tspan>
                  </text>
                  <text
                    id="Line_Number_6_133_"
                    data-name="Line Number 6 133°"
                    transform="translate(8504.806 -2093)"
                    fill="#fff"
                    :class="{ activeName: curLine.name == 6 }"
                    font-size="10"
                    font-family="MicrosoftYaHei, Microsoft YaHei"
                  >
                    <tspan x="-47.791" y="0">Line Number 6 133°</tspan>
                  </text>
                  <text
                    id="Line_Number_5_129_"
                    data-name="Line Number 5 129°"
                    transform="translate(8517.806 -2110)"
                    fill="#fff"
                    :class="{ activeName: curLine.name == 5 }"
                    font-size="10"
                    font-family="MicrosoftYaHei, Microsoft YaHei"
                  >
                    <tspan x="-47.791" y="0">Line Number 5 129°</tspan>
                  </text>
                  <text
                    id="Line_Number_14_313_"
                    data-name="Line Number 14 313°"
                    transform="translate(8289.806 -2224)"
                    fill="#fff"
                    :class="{ activeName: curLine.name == 14 }"
                    font-size="10"
                    font-family="MicrosoftYaHei, Microsoft YaHei"
                  >
                    <tspan x="-50.723" y="0">Line Number 14 313°</tspan>
                  </text>
                  <text
                    id="Line_Number_13_309_"
                    data-name="Line Number 13 309°"
                    transform="translate(8274.806 -2207)"
                    fill="#fff"
                    :class="{ activeName: curLine.name == 13 }"
                    font-size="10"
                    font-family="MicrosoftYaHei, Microsoft YaHei"
                  >
                    <tspan x="-50.723" y="0">Line Number 13 309°</tspan>
                  </text>
                  <circle
                    id="椭圆_114"
                    data-name="椭圆 114"
                    cx="2"
                    cy="2"
                    r="2"
                    transform="translate(8324.806 -2211)"
                    fill="#fff"
                  />
                  <circle
                    id="椭圆_118"
                    data-name="椭圆 118"
                    cx="2"
                    cy="2"
                    r="2"
                    transform="translate(8324.806 -2117)"
                    fill="#fff"
                  />
                  <circle
                    id="椭圆_119"
                    data-name="椭圆 119"
                    cx="2"
                    cy="2"
                    r="2"
                    transform="translate(8337.806 -2103)"
                    fill="#fff"
                  />
                  <circle
                    id="椭圆_120"
                    data-name="椭圆 120"
                    cx="2"
                    cy="2"
                    r="2"
                    transform="translate(8358.806 -2089)"
                    fill="#fff"
                  />
                  <circle
                    id="椭圆_121"
                    data-name="椭圆 121"
                    cx="2"
                    cy="2"
                    r="2"
                    transform="translate(8381.806 -2082)"
                    fill="#fff"
                  />
                  <circle
                    id="椭圆_115"
                    data-name="椭圆 115"
                    cx="2"
                    cy="2"
                    r="2"
                    transform="translate(8338.806 -2226)"
                    fill="#fff"
                  />
                  <circle
                    id="椭圆_116"
                    data-name="椭圆 116"
                    cx="2"
                    cy="2"
                    r="2"
                    transform="translate(8359.806 -2240)"
                    fill="#fff"
                  />
                  <circle
                    id="椭圆_117"
                    data-name="椭圆 117"
                    cx="2"
                    cy="2"
                    r="2"
                    transform="translate(8382.806 -2247)"
                    fill="#fff"
                  />
                  <circle
                    id="椭圆_122"
                    data-name="椭圆 122"
                    cx="2"
                    cy="2"
                    r="2"
                    transform="translate(8406.806 -2246)"
                    fill="#fff"
                  />
                  <circle
                    id="椭圆_123"
                    data-name="椭圆 123"
                    cx="2"
                    cy="2"
                    r="2"
                    transform="translate(8429.806 -2239)"
                    fill="#fff"
                  />
                  <circle
                    id="椭圆_124"
                    data-name="椭圆 124"
                    cx="2"
                    cy="2"
                    r="2"
                    transform="translate(8449.806 -2226)"
                    fill="#fff"
                  />
                  <circle
                    id="椭圆_125"
                    data-name="椭圆 125"
                    cx="2"
                    cy="2"
                    r="2"
                    transform="translate(8461.806 -2211)"
                    fill="#fff"
                  />
                  <circle
                    id="椭圆_126"
                    data-name="椭圆 126"
                    cx="2"
                    cy="2"
                    r="2"
                    transform="translate(8461.806 -2117)"
                    fill="#fff"
                  />
                  <circle
                    id="椭圆_127"
                    data-name="椭圆 127"
                    cx="2"
                    cy="2"
                    r="2"
                    transform="translate(8449.806 -2103)"
                    fill="#fff"
                  />
                  <circle
                    id="椭圆_128"
                    data-name="椭圆 128"
                    cx="2"
                    cy="2"
                    r="2"
                    transform="translate(8428.806 -2088)"
                    fill="#fff"
                  />
                  <circle
                    id="椭圆_129"
                    data-name="椭圆 129"
                    cx="2"
                    cy="2"
                    r="2"
                    transform="translate(8405.806 -2082)"
                    fill="#fff"
                  />
                  <g
                    id="组_705"
                    data-name="组 705"
                    transform="translate(8327.756 -2127.417)"
                  >
                    <line
                      id="直线_132-2"
                      data-name="直线 132"
                      x1="34.544"
                      y2="12.942"
                      transform="translate(0 0)"
                      fill="none"
                      stroke="#fff"
                      stroke-width="0.5"
                    />
                    <line
                      id="直线_133-2"
                      data-name="直线 133"
                      x1="24.263"
                      y2="26.329"
                      transform="translate(12.094 0.906)"
                      fill="none"
                      stroke="#fff"
                      stroke-width="0.5"
                    />
                    <line
                      id="直线_134-2"
                      data-name="直线 134"
                      x1="6.091"
                      y2="39.522"
                      transform="translate(32.983 0.906)"
                      fill="none"
                      stroke="#fff"
                      stroke-width="0.5"
                    />
                    <line
                      id="直线_135-2"
                      data-name="直线 135"
                      x2="14.279"
                      y2="46.119"
                      transform="translate(41.792 0.906)"
                      fill="none"
                      stroke="#fff"
                      stroke-width="0.5"
                    />
                  </g>
                  <g
                    id="组_706"
                    data-name="组 706"
                    transform="translate(8408.016 -2243.409)"
                  >
                    <line
                      id="直线_132-3"
                      data-name="直线 132"
                      y1="12.942"
                      x2="34.544"
                      transform="translate(21.528 34.083)"
                      fill="none"
                      stroke="#fff"
                      stroke-width="0.5"
                    />
                    <line
                      id="直线_133-3"
                      data-name="直线 133"
                      y1="26.329"
                      x2="24.263"
                      transform="translate(19.716 19.79)"
                      fill="none"
                      stroke="#fff"
                      stroke-width="0.5"
                    />
                    <line
                      id="直线_134-3"
                      data-name="直线 134"
                      y1="39.522"
                      x2="6.091"
                      transform="translate(16.997 6.597)"
                      fill="none"
                      stroke="#fff"
                      stroke-width="0.5"
                    />
                    <line
                      id="直线_135-3"
                      data-name="直线 135"
                      x1="14.279"
                      y1="46.119"
                      transform="translate(0 0)"
                      fill="none"
                      stroke="#fff"
                      stroke-width="0.5"
                    />
                  </g>
                  <g
                    id="组_707"
                    data-name="组 707"
                    transform="translate(8408.016 -2127.966)"
                  >
                    <line
                      id="直线_132-4"
                      data-name="直线 132"
                      x2="34.544"
                      y2="12.942"
                      transform="translate(21.528 0)"
                      fill="none"
                      stroke="#fff"
                      stroke-width="0.5"
                    />
                    <line
                      id="直线_133-4"
                      data-name="直线 133"
                      x2="24.263"
                      y2="26.329"
                      transform="translate(19.716 0.906)"
                      fill="none"
                      stroke="#fff"
                      stroke-width="0.5"
                    />
                    <line
                      id="直线_134-4"
                      data-name="直线 134"
                      x2="6.091"
                      y2="39.522"
                      transform="translate(16.997 0.906)"
                      fill="none"
                      stroke="#fff"
                      stroke-width="0.5"
                    />
                    <line
                      id="直线_135-4"
                      data-name="直线 135"
                      x1="14.279"
                      y2="46.119"
                      transform="translate(0 0.906)"
                      fill="none"
                      stroke="#fff"
                      stroke-width="0.5"
                    />
                  </g>
                </g>
              </svg>
            </div>
            <div class="partItem">
              <img
                class="chartImg"
                style="width: 100%; height: 70%"
                :src="curLine.img"
                v-if="curLine"
              />
              <img
                class="chartImg"
                style="width: 100%; height: 70%"
                src="@/assets/images/mooring/total.png"
                v-else
              />
            </div>
            <div class="partItem infoPart" v-if="curLine">
              <div class="valueBox">
                <div class="title">平台链</div>
                <div class="valueItem">
                  <div class="label">半径(mm)</div>
                  <div class="value">{{ curLine.info1.r }}</div>
                </div>
                <div class="valueItem">
                  <div class="label">破断张力(kN)</div>
                  <div class="value">{{ curLine.info1.breakF }}</div>
                </div>
                <div class="valueItem">
                  <div class="label">长度(m)</div>
                  <div class="value">{{ curLine.info1.L }}</div>
                </div>
              </div>
              <div class="valueBox">
                <div class="title">导缆器(m)</div>
                <div class="valueItem">
                  <div class="label">X</div>
                  <div class="value">{{ curLine.info2.x }}</div>
                </div>
                <div class="valueItem">
                  <div class="label">Y</div>
                  <div class="value">{{ curLine.info2.y }}</div>
                </div>
                <div class="valueItem">
                  <div class="label">Z</div>
                  <div class="value">{{ curLine.info2.z }}</div>
                </div>
              </div>
              <div class="valueBox">
                <div class="title">聚酯缆</div>
                <div class="valueItem">
                  <div class="label">半径(mm)</div>
                  <div class="value">{{ curLine.info3.r }}</div>
                </div>
                <div class="valueItem">
                  <div class="label">破断张力(kN)</div>
                  <div class="value">{{ curLine.info3.breakF }}</div>
                </div>
                <div class="valueItem">
                  <div class="label">长度(m)</div>
                  <div class="value">{{ curLine.info3.L }}</div>
                </div>
              </div>
              <div class="valueBox">
                <div class="title">锚定点(m)</div>
                <div class="valueItem">
                  <div class="label">X</div>
                  <div class="value">{{ curLine.info4.x }}</div>
                </div>
                <div class="valueItem">
                  <div class="label">Y</div>
                  <div class="value">{{ curLine.info4.y }}</div>
                </div>
                <div class="valueItem">
                  <div class="label">水深</div>
                  <div class="value">{{ curLine.info4.z }}</div>
                </div>
              </div>
              <div class="valueBox">
                <div class="title">底链</div>
                <div class="valueItem">
                  <div class="label">半径(mm)</div>
                  <div class="value">{{ curLine.info5.r }}</div>
                </div>
                <div class="valueItem">
                  <div class="label">破断张力(kN)</div>
                  <div class="value">{{ curLine.info5.breakF }}</div>
                </div>
                <div class="valueItem">
                  <div class="label">长度(m)</div>
                  <div class="value">{{ curLine.info5.L }}</div>
                </div>
              </div>
              <div class="valueBox">
                <div class="title">最大拉力(KN)</div>
                <div class="valueItem single">
                  <div class="value">{{ curLine.value }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="svgRight">
          <div style="width: 100%; height: 100%" id="chart3"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from "moment";
import { instance } from "@/api/config";
export default {
  name: "calculate",
  props: {},
  data() {
    return {
      fengxiang: "",
      fengsu: "",
      blfx: "",
      blgd: "",
      blzq: "",
      hllx: "",
      bcls: "",
      dcls: "",
      sjbc: "",
      config: [
        {
          prop: "col1",
          label: "1",
        },
        {
          prop: "col2",
          label: "2",
        },
        {
          prop: "col3",
          label: "3",
        },
        {
          prop: "col4",
          label: "4",
        },
        {
          prop: "col5",
          label: "5",
        },
        {
          prop: "col6",
          label: "6",
        },
        {
          prop: "col7",
          label: "7",
        },
        {
          prop: "col8",
          label: "8",
        },
        {
          prop: "col9",
          label: "9",
        },
        {
          prop: "col10",
          label: "10",
        },
        {
          prop: "col11",
          label: "11",
        },
        {
          prop: "col12",
          label: "12",
        },
        {
          prop: "col13",
          label: "13",
        },
        {
          prop: "col14",
          label: "14",
        },
        {
          prop: "col15",
          label: "15",
        },
        {
          prop: "col16",
          label: "16",
        },
      ],
      tableData: [
        {
          type: "最大张力/KN",
          col1: 34,
          col2: 32,
          col3: 56,
          col4: 88,
          col5: 34,
          col6: 32,
          col7: 56,
          col8: 88,
          col9: 34,
          col10: 32,
          col11: 56,
          col12: 88,
          col13: 34,
          col14: 32,
          col15: 56,
          col16: 88,
        },
        {
          type: "最小张力/KN",
          col1: 34,
          col2: 32,
          col3: 56,
          col4: 88,
          col5: 34,
          col6: 32,
          col7: 56,
          col8: 88,
          col9: 34,
          col10: 32,
          col11: 56,
          col12: 88,
          col13: 34,
          col14: 32,
          col15: 56,
          col16: 88,
        },
      ],
      lineInfoList: [
        {
          name: 1,
          value: "",
          img: require("@/assets/images/mooring/1.png"),
          info1: { r: 157, breakF: 23559, L: 244 },
          info2: { x: 31.7, y: 47.7, z: 3.2 },
          info3: { r: 256, breakF: 21437, L: 1912 },
          info4: { x: 1231.5, y: 1529.2, z: 1422.8 },
          info5: { r: 157, breakF: 23559, L: 259 },
          info6: {},
        },
        {
          name: 2,
          value: "",
          img: require("@/assets/images/mooring/2.png"),
          info1: { r: 157, breakF: 23559, L: 244 },
          info2: { x: 35.4, y: 47.6, z: 3.2 },
          info3: { r: 256, breakF: 21437, L: 1912 },
          info4: { x: 1335.6, y: 1441.9, z: 1422.8 },
          info5: { r: 157, breakF: 23559, L: 259 },
          info6: {},
        },
        {
          name: 3,
          value: "",
          img: require("@/assets/images/mooring/3.png"),
          info1: { r: 157, breakF: 23559, L: 244 },
          info2: { x: 39.0, y: 47.5, z: 3.2 },
          info3: { r: 256, breakF: 21437, L: 1912 },
          info4: { x: 1433.3, y: 1347.7, z: 1422.8 },
          info5: { r: 157, breakF: 23559, L: 259 },
          info6: {},
        },
        {
          name: 4,
          value: "",
          img: require("@/assets/images/mooring/4.png"),
          info1: { r: 157, breakF: 23559, L: 244 },
          info2: { x: 42.7, y: 47.5, z: 3.2 },
          info3: { r: 256, breakF: 21437, L: 1912 },
          info4: { x: 1524.3, y: 1247.2, z: 1422.8 },
          info5: { r: 157, breakF: 23559, L: 259 },
          info6: {},
        },
        {
          name: 5,
          value: "",
          img: require("@/assets/images/mooring/5.png"),
          info1: { r: 157, breakF: 23559, L: 244 },
          info2: { x: 42.7, y: -47.4, z: 3.2 },
          info3: { r: 256, breakF: 21437, L: 1912 },
          info4: { x: 1501.0, y: -1228.3, z: 1447.9 },
          info5: { r: 157, breakF: 23559, L: 259 },
          info6: {},
        },
        {
          name: 6,
          value: "",
          img: require("@/assets/images/mooring/6.png"),
          info1: { r: 157, breakF: 23559, L: 244 },
          info2: { x: 39.0, y: -47.5, z: 3.2 },
          info3: { r: 256, breakF: 21437, L: 1912 },
          info4: { x: 1411.4, y: 1327.3, z: 1447.9 },
          info5: { r: 157, breakF: 23559, L: 259 },
          info6: {},
        },
        {
          name: 7,
          value: "",
          img: require("@/assets/images/mooring/7.png"),
          info1: { r: 157, breakF: 23559, L: 244 },
          info2: { x: 35.4, y: -47.6, z: 3.2 },
          info3: { r: 256, breakF: 21437, L: 1912 },
          info4: { x: 1315.1, y: -1419.9, z: 1447.9 },
          info5: { r: 157, breakF: 23559, L: 259 },
          info6: {},
        },
        {
          name: 8,
          value: "",
          img: require("@/assets/images/mooring/8.png"),
          info1: { r: 157, breakF: 23559, L: 244 },
          info2: { x: 31.7, y: -47.7, z: 3.2 },
          info3: { r: 256, breakF: 21437, L: 1912 },
          info4: { x: 1212.6, y: -1505.9, z: 1447.9 },
          info5: { r: 157, breakF: 23559, L: 259 },
          info6: {},
        },
        {
          name: 9,
          value: "",
          img: require("@/assets/images/mooring/9.png"),
          info1: { r: 157, breakF: 23559, L: 244 },
          info2: { x: -31.7, y: -47.7, z: 3.2 },
          info3: { r: 256, breakF: 21437, L: 1912 },
          info4: { x: -1237.7, y: -1537.1, z: 1420.8 },
          info5: { r: 157, breakF: 23559, L: 259 },
          info6: {},
        },
        {
          name: 10,
          value: "",
          img: require("@/assets/images/mooring/10.png"),
          info1: { r: 157, breakF: 23559, L: 244 },
          info2: { x: -35.4, y: -47.6, z: 3.2 },
          info3: { r: 256, breakF: 21437, L: 1912 },
          info4: { x: -1432.3, y: -1449.2, z: 1420.8 },
          info5: { r: 157, breakF: 23559, L: 259 },
          info6: {},
        },
        {
          name: 11,
          value: "",
          img: require("@/assets/images/mooring/11.png"),
          info1: { r: 157, breakF: 23559, L: 244 },
          info2: { x: -39, y: -47.5, z: 3.2 },
          info3: { r: 256, breakF: 21437, L: 1912 },
          info4: { x: -1440.6, y: 1354.6, z: 1420.8 },
          info5: { r: 157, breakF: 23559, L: 259 },
          info6: {},
        },
        {
          name: 12,
          value: "",
          img: require("@/assets/images/mooring/12.png"),
          info1: { r: 157, breakF: 23559, L: 244 },
          info2: { x: -42.7, y: -47.5, z: 3.2 },
          info3: { r: 256, breakF: 21437, L: 1912 },
          info4: { x: -1532, y: -1253.6, z: 1420.8 },
          info5: { r: 157, breakF: 23559, L: 259 },
          info6: {},
        },
        {
          name: 13,
          value: "",
          img: require("@/assets/images/mooring/13.png"),
          info1: { r: 157, breakF: 23559, L: 244 },
          info2: { x: -42.7, y: 47.5, z: 3.2 },
          info3: { r: 256, breakF: 21437, L: 1912 },
          info4: { x: -1539.8, y: 1259.7, z: 1396.2 },
          info5: { r: 157, breakF: 23559, L: 259 },
          info6: {},
        },
        {
          name: 14,
          value: "",
          img: require("@/assets/images/mooring/14.png"),
          info1: { r: 157, breakF: 23559, L: 244 },
          info2: { x: -39.1, y: 47.5, z: 3.2 },
          info3: { r: 256, breakF: 21437, L: 1912 },
          info4: { x: -1447.9, y: 1361.4, z: 1396.2 },
          info5: { r: 157, breakF: 23559, L: 259 },
          info6: {},
        },
        {
          name: 15,
          value: "",
          img: require("@/assets/images/mooring/15.png"),
          info1: { r: 157, breakF: 23559, L: 244 },
          info2: { x: -35.4, y: 47.6, z: 3.2 },
          info3: { r: 256, breakF: 21437, L: 1912 },
          info4: { x: -1349.1, y: 1456.6, z: 1396.2 },
          info5: { r: 157, breakF: 23559, L: 259 },
          info6: {},
        },
        {
          name: 16,
          value: "",
          img: require("@/assets/images/mooring/16.png"),
          info1: { r: 157, breakF: 23559, L: 244 },
          info2: { x: -31.7, y: 47.7, z: 3.2 },
          info3: { r: 256, breakF: 21437, L: 1912 },
          info4: { x: 1244.0, y: 1544.8, z: 1396.2 },
          info5: { r: 157, breakF: 23559, L: 259 },
          info6: {},
        },
      ],
      curLine: "",
      curMooringDataInfo: [],
      mooringDataInfo: [],
    };
  },
  computed: {
    curTheme() {
      return this.$store.state.curTheme;
    },
  },
  methods: {
    jisuan() {
      this.queryF1();
    },
    queryF1() {
      //应力实时监测
      instance
        .get("mooringStressMonitor/realTimeStressMonitoring")
        .then((res) => {
          if (res.code == 200) {
            let resData = res.data;
            this.mooringDataInfo = resData;
            this.getChart1();
          }
        });
    },
    getChart1() {
      let xArr = [];
      let dataArr = [];
      this.mooringDataInfo.forEach((item) => {
        xArr.push(item.hybridMooringLineNum);
        dataArr.push(item.maxTension);
      });
      //   xArr = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]
      //   dataArr = [30, 35, 32, 39, 40, 45, 49, 48, 20, 21, 32, 36, 15, 16, 22, 26]
      let colorStr = this.curTheme == 1 ? "#ffffff" : "#000000";
      let myChart = echarts.init(document.getElementById("chart1"));
      this.curLine = this.lineInfoList[0];
      this.curLine.value = dataArr[0];
      this.queryF2();
      myChart.on("click", (params) => {
        let name = params.name;
        let tempInfo = this.lineInfoList.find((t) => t.name == name);
        console.log("图表点击", params, tempInfo);
        tempInfo.value = params.value;
        this.curLine = tempInfo;
        this.queryF2();
      });
      // 指定图表的配置项和数据
      let option = {
        legend: {
          show: false,
          textStyle: {
            color: colorStr,
          },
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
            label: {
              backgroundColor: "#6a7985",
            },
          },
          formatter: "系泊编号: {b}<br />{a}: {c}KN",
        },
        grid: {
          left: 80,
          top: 30,
          right: 20,
          bottom: 35,
        },
        xAxis: {
          type: "category",
          nameTextStyle: {
            color: colorStr,
          },
          axisTick: {
            lineStyle: {
              color: colorStr,
            },
          },
          axisLine: {
            lineStyle: {
              color: colorStr,
            },
            show: true,
          },
          data: xArr,
        },
        yAxis: {
          type: "value",
          name: "最大拉力",
          axisLine: {
            lineStyle: {
              color: colorStr,
            },
            show: true,
          },
          splitLine: {
            lineStyle: {
              color: "#CCCCCC",
            },
            show: true,
          },
          nameLocation: "center",
          nameGap: 55,
        },
        series: [
          {
            data: dataArr,
            type: "bar",
            name: "最大拉力",
            barWidth: 10,
            itemStyle: {
              borderRadius: 36,
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "#1BDEFE", // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "#2C81DA", // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
          },
        ],
      };

      myChart.setOption(option, true);
    },
    queryF2() {
      //应力智能预测
      let tempParams = {
        num: this.curLine.name,
      };
      instance
        .get("mooringStressMonitor/stressIntelligentPrediction", {
          params: tempParams,
        })
        .then((res) => {
          console.log("数据结果", res);
          if (res.code == 200) {
            let resData = res.data;
            this.curMooringDataInfo = resData;
            this.getChart3();
          }
        });
    },
    getChart3() {
      if (!this.curMooringDataInfo.length) {
        return;
      }
      let xArr = [];
      let dataArr1 = [];
      let dataArr2 = [];
      let dataArr3 = [];
      this.curMooringDataInfo.forEach((item) => {
        xArr.push(moment(item.realTime).format("YYYY/MM/DD HH:mm:ss"));
        dataArr1.push((item.maxTension * 1).toFixed(2));
        dataArr2.push((item.maxTensionPred * 1).toFixed(2));
        dataArr3.push(
          Math.abs(item.maxTension * 1 - item.maxTensionPred * 1).toFixed(2)
        );
      });
      let maxErr = Math.max(...dataArr3);
      let myChart = echarts.init(document.getElementById("chart3"));
      let colorStr = this.curTheme == 1 ? "#ffffff" : "#000000";
      console.log("colorStr", colorStr);

      // 指定图表的配置项和数据
      let option = {
        legend: {
          show: true,
          textStyle: {
            color: colorStr,
          },
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
            label: {
              backgroundColor: "#6a7985",
            },
          },
        },
        grid: {
          left: "50",
          top: "50",
          right: "40",
          bottom: "70",
        },
        xAxis: {
          nameTextStyle: {
            color: colorStr,
          },
          axisTick: {
            lineStyle: {
              color: colorStr,
            },
          },
          axisLine: {
            lineStyle: {
              color: colorStr,
            },
            show: true,
          },
          axisLabel: {
            formatter: function (value, index) {
              return moment(value).format("HH:mm");
            },
          },
          type: "category",
          data: xArr,
        },
        yAxis: [
          {
            type: "value",
            name: "拉力值",
            position: "left",
            min: "dataMin",
            axisLine: {
              lineStyle: {
                color: colorStr,
              },
              show: true,
            },
            splitLine: {
              lineStyle: {
                color: colorStr,
              },
              show: false,
            },
            axisLabel: {
              formatter: function (value, index) {
                return parseInt(value);
              },
            },
          },
          {
            type: "value",
            name: "误差",
            show: false,
            position: "right",
            min: 0,
            max: maxErr * 4,
            alignTicks: true,
            axisLine: {
              show: true,
              lineStyle: {
                color: colorStr,
              },
            },
            axisLabel: {
              formatter: "{value}",
              color: "#0A87E7",
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "#2964BB",
                type: "dashed",
              },
            },
            nameLocation: "center",
            nameGap: 35,
          },
        ],
        dataZoom: [
          {
            type: "slider",
            start: 0,
            end: 100,
            height: 15,
          },
        ],
        series: [
          {
            data: dataArr1,
            type: "line",
            smooth: true,
            showSymbol: false,
            // yAxisIndex: 0,
            name: "实时",
            lineStyle: {
              color: "#FF3636",
            },
          },
          {
            data: dataArr2,
            type: "line",
            smooth: true,
            showSymbol: false,
            // yAxisIndex: 1,
            name: "预测",
            lineStyle: {
              color: "#00FBFF",
            },
          },
          {
            data: dataArr3,
            type: "line",
            smooth: true,
            show: false,
            yAxisIndex: 1,
            showSymbol: false,
            name: "误差",
            lineStyle: {
              color: "transparent",
            },
          },
        ],
      };

      myChart.setOption(option, true);
    },
  },
  watch: {},
  mounted() {},
};
</script>

<style lang="scss" scoped>
/* cf-ui 主题样式覆盖 深色*/
html[data-theme="dark"] {
  .chartPart {
    background-image: url("@/assets/images/mooring/黑背景.png");
  }
  .left {
    .title {
      color: white;
    }
    .item {
      color: white;
    }
    .progress {
      color: white;
    }
  }
  .infoPart {
    background: transparent;
  }
  .valueBox {
    height: 23% !important;
    border-radius: 0;
    overflow: initial;
    padding-top: 5px;
    background: transparent !important;
    background-image: url("@/assets/images/mooring/infoBox.png") !important;
    background-size: 100% 100% !important;
    background-repeat: no-repeat !important;
    .title {
      padding: 0;
      margin-top: -5px;
      color: #ffffff;
      background: transparent;
    }
    .label {
      // padding: 0;
      color: #00cdff;
      background: transparent;
    }
    .value {
      // padding: 0;
      color: #ffffff !important;
      background: transparent;
    }
  }
}
.main {
  display: flex;
  // height: 600px;
  .left {
    width: 15%;
    margin-right: 20px;
    padding-right: 10px;
    border-right: 1px solid #3e77c3;
    .title {
      font-size: 14px;
      font-weight: bold;
      //   color: white;
    }
    .item {
      width: 220px;
      display: flex;
      align-items: center;
      //   color: white;
      margin-top: 8px;
      font-size: 12px;
      .label {
        width: 60px;
      }
      .value {
        width: 120px;
        margin-right: 10px;
      }
    }
    .line {
      margin: 20px 0px;
      border-bottom: 1px solid #3e77c3;
    }
    .progress {
      //   color: white;
      font-size: 12px;
      .text {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
      }
    }
    .btn {
      margin-top: 20px;
    }
  }
  .right {
    flex: 1;
    .chartBox {
      width: 100%;
      height: 160px;
    }
    .svgBox {
      height: 330px;
      display: flex;
      .svgLeft {
        width: 60%;
        height: 100%;
        margin-right: 20px;
        .chartPart {
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: space-between;
          background-image: url("@/assets/images/mooring/白背景.png");
          background-size: 100% 100%;
          .partItem {
            width: 33%;
            height: 100%;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-items: center;
          }
          .infoPart {
            overflow: auto;
          }
          .activeName {
            fill: #00fbff;
          }
          .valueBox {
            width: calc(50% - 5px);
            height: 30%;
            background: #ffffff;
            color: #000000;
            border-radius: 10px;
            padding-bottom: 5px;
            overflow: hidden;
            .title {
              width: 100%;
              font-size: 10px;
              text-align: center;
            }
            .valueItem {
              display: flex;
              justify-content: space-between;
              font-size: 10px;
              overflow: hidden;
            }
            .value {
              flex: 1;
              padding: 2px 5px 2px 0;
              text-align: center;
              color: #3578f6;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .valueItem.single {
              height: 100%;
              .value {
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 40px;
              }
            }
            .label {
              width: 70px;
              padding: 2px 0 2px 5px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
      .svgRight {
        flex: 1;
        height: 100%;
      }
    }
  }
}
:deep(.el-input__inner) {
  height: 30px;
  border-radius: 0px;
}
</style>
