<template>
  <el-dialog :visible.sync="dialogVisible" :modal="false" draggable :append-to-body="false"
    custom-class="custom_dialog_content" :width="width" :z-index="100" :class="customClass" @close="close">
    <template #title>
      <slot name="title">
        <div class="header">
          <div class="header-bg">{{ title }}</div>
        </div>
      </slot>
      <!-- <img class="close-icon" :src="closeBtnSrc" @click="close" /> -->
    </template>
    <template #default>
      <CardBackground2 />
      <slot name="default"></slot>
    </template>
    <!-- 可以用renderFooter控制是否渲染footer，也可以外部使用插槽逻辑，覆盖当前得默认插槽 -->
    <template #footer v-if="renderFooter">
      <slot name="footer">
        <span class="dialog-footer">
          <el-button type="primary" class="cancel" @click="cancel">取消</el-button>
          <el-button :loading="loading" type="success" class="confirm" @click="confirm"> 确认 </el-button>
        </span>
      </slot>
    </template>
  </el-dialog>
</template>
<script lang="ts">
// import closeBtImg from '@/assets/images/big-close_white.png'
import CardBackground2 from '@/components/card-background2/card-background2.vue'
export default {
  name: "KdicDialog",
  data() {
    return {
      dialogVisible: false,
      loading: false,
      // closeBtnSrc: closeBtImg
    };
  },
  props: {
    title: {
      default: ''
    },
    visible: {
      default: false
    },
    customClass: {
      default: ''
    },
    renderFooter: {
      default: true
    },
    width: {
      default: '50%'
    }
  },
  components: { CardBackground2 },
  mounted() {

  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    }
  },
  methods: {
    confirm() {
      this.$emit('confirm')
    },
    cancel() {
      this.dialogVisible = false;
      this.$emit('update:visible', false)
    },
    close() {
      this.cancel()
    }
  }
}
</script>
<style lang="scss" scoped>
.close-icon {
  position: absolute;
  right: 14px;
  top: 14px;
  width: 18px;
  height: 18px;
  cursor: pointer;
  z-index: 9;
}
</style>
<style lang="scss">
.custom_dialog_content {
  // background: linear-gradient(180deg,#0E2341B3, #091629B3, #010103B3);

  .el-dialog__header {
    padding: 20px 20px 10px;
  }
  .el-dialog__close {
    color: black;
  }

  .header {
    width: 100%;
    height: 30px;

    .header-bg {
      text-align: center;
      width: 285px;
      height: 34px;
      margin: 0 auto;
      // background-image: url('@/assets/images/title-bg.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      color: black;
      font-size: 18px;
    }
  }

  .el-dialog__body {
    min-height: 300px;
    // background: linear-gradient(180deg,#0E2341B3, #091629B3, #010103B3);
  }

  .el-dialog__footer {
    text-align: center;
  }
}
/* cf-ui 主题样式覆盖 深色*/
html[data-theme='dark'] {
  .custom_dialog_content {
    background: linear-gradient(180deg, rgba(14, 35, 65, 0.9) -3.3%, rgba(9, 22, 41, 0.9) 46.89%, rgba(1, 1, 3, 0.70) 125.4%);
    .el-dialog__close {
      color: #2465AF;
    }
    .header {
      .header-bg {
        background-image: url('@/assets/images/title-bg.png');
        color: #F7B500;
      }
    }
  }
}
</style>