import Vue from 'vue';
import Vuex from 'vuex';
Vue.use(Vuex);

let rootHtml = document.documentElement
let tempTheme = rootHtml.getAttribute('data-theme')
rootHtml = null

const store = new Vuex.Store({
  state: {
    permissionList: [],
    curTheme: tempTheme == 'dark' ? 1 : 0,
  },
  mutations: {
    changeTheme(state, data) {
      console.log('vuex修改主题', data)
      state.curTheme = data
    }
  },
  actions: {

  },
  modules: {
  },
});
export default store;
