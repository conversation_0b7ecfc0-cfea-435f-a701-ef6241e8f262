
const changeRouter = {
    path: '/business',
    nameCN: '业务管理',
    component: () => import('@/views/businessAnalysis/index.vue'),
    children: [
        {
            path: 'equipment',
            name: 'equipment',
            nameCN: '设备维修分析',
            component: () => import('@/views/businessAnalysis/equipment/index.vue'),
        },
        {
            path: 'oil',
            name: 'oil',
            nameCN: '油气水',
            component: () => import('@/views/businessAnalysis/ogw/index.vue'),
        },
        {
            path: 'prodEnviTrack',
            name: 'prodEnviTrack',
            nameCN: '生产环境跟踪',
            component: () => import('@/views/businessAnalysis/prodEnviTrack/index.vue'),
        }
    ]
}

export default changeRouter