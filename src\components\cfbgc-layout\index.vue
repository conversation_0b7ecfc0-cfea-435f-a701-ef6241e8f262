<template>
    <div class="cfbgc-layout" :class="personalized">
        <slot name="default"></slot>
    </div>
</template>
<script>
export default {
    name: "cfbgcLayout",
    props: {
        personalized: {
            type: String,
            default() {
                return "";
            },
        },
    },
};
</script>
<style lang="scss">
.cfbgc-layout {
    width: 100%;
    height: 100%;
    overflow: hidden;
    top: 0.2rem;
    &.style1 {
        // background: url("/static/imgs/app_bg_style1.jpg");
        background-size: 100% 100%;
        padding: 3em 2em 2em 2em;
    }
    &.style2 {
        // background: url("/static/imgs/app_bg_style2.png");
        background-size: 100% 100%;
        padding: 4.1rem 3.2rem 3rem 3rem;
    }
}

[data-theme="dark"] .cfbgc-layout {
    background: #121635;
}

[data-theme="tint"] .cfbgc-layout {
    background: #E7EFF9;
}
</style>
