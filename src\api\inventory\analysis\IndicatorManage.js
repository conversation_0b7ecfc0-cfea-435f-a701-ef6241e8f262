import {instance} from "../../config";

//库存指标列表查询
export function Indicators_pageList(data) {
    return instance({
        url: '/im-ls/sapInfoIndex/condition',
        method: "POST",
        data
    });
}

//查询指标
export function Indicators_query(params) {
    return instance({
        url: '/im-ls/sapInfoIndex',
        method: "GET",
        params
    });
}

//新增或更新指标
export function Indicators_add_edit(data) {
    return instance({
        url: '/im-ls/sapInfoIndex',
        method: "POST",
        data
    });
}

//删除指标
export function Indicators_del(params) {
    return instance({
        url: '/im-ls/sapInfoIndex/remove',
        method: "POST",
        params
    });
}

// 校验指标是否存在
export function Indicators_check(params) {
    return instance({
        url: '/im-ls/sapInfoIndex/check',
        method: "GET",
        params
    });
}

// 导出指标
export function Indicators_export(data) {
    return instance({
        url: '/im-ls/sapInfoIndex/export',
        method: "POST",
        responseType: "blob",
        data
    });
}
//查看指标操作日志
export function Indicators_record(params) {
    return instance({
        url: '/im-ls/sapInfoIndex/log',
        method: "GET",
        params
    });
}
