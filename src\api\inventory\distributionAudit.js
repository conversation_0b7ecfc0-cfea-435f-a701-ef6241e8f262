// 标签分页列表查询

import {instance} from "../config";

//查询已办
export function done_have(params) {
    return instance({
        url: '/grm-ls/process/done',
        method: "GET",
        params
    });
}

//查询待办
export function gettoDo(params) {
    return instance({
        url: '/grm-ls/process/toDo',
        method: "GET",
        params
    });
}

/*查看编辑流动保障报告*/
export function matchGroupReport_get(params) {
    return instance({
        url: '/ufgts-ls/matchGroupReport',
        method: "get",
        params
    });
}

/*查看编工艺保障报告*/
export function platformReport_get(params) {
    return instance({
        url: '/ufgts-ls/platformReport',
        method: "get",
        params
    });
}

//流程历史分页查询
export function procHistory_query(params) {
    return instance({
        url: '/grm-ls/process',
        method: "get",
        params
    });
}
//配产方案审核
export function processcheck(data) {
    return instance({
        url: '/grm-ls/process/check',
        method: "POST",
        data
    });
}


//查看配产方案详情
export function productPlan_detail(params) {
    return instance({
        url: '/grm-ls/process/demandPlanResult',
        method: "get",
        params
    });
}

//待办配产方案审核查询详情
export function todo_detail(params) {
    return instance({
        url: '/grm-ls/process/todo/detail',
        method: "get",
        params
    });
}

//已办配产方案审核查询详情
export function done_detail(params) {
    return instance({
        url: '/grm-ls/process/done/detail',
        method: "get",
        params
    });
}

//已办导出
export function done_export(params) {
    return instance({
        url: '/grm-ls/process/done/export',
        method: "post",
        params,
        responseType: "blob",
    });
}
//待办导出
export function todo_export(params) {
    return instance({
        url: '/grm-ls/process/todo/export',
        method: "post",
        params,
        responseType: "blob",
    });
}

//一般通过
export function donePass(params) {
    return instance({
        url: '/grm-ls/process/donePass',
        method: "GET",
        params
    });
}

