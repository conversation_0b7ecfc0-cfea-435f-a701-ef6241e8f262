<template>
  <div class="oil-cost">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  name: "oilCost",
  data() {
    return {
      xData: [
        "1月",
        "2月",
        "3月",
        "4月",
        "5月",
        "6月",
        "7月",
        "8月",
        "9月",
        "10月",
        "11月",
        "12月",
      ],
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  methods: {
    initChart() {
      let mychart = echarts.init(this.$refs.chartBox);
      this.option = {
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: ["利润总额", "油气均价", "净销量", "完成成本"],
          textStyle: {
            color: "#ffffff",
            fontSize: 12,
          },
          icon: "rect",
          itemWidth: 8,
          itemHeight: 8,
          right: "10%",
          top: "2%",
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: this.xData,
          axisTick: {
            show: false,
          },
        },

        yAxis: {
          type: "value",
          splitLine: {
            show: false,
          },
        },
        series: [
          {
            name: "完成成本",
            type: "line",
            data: [120, 132, 101, 134, 90, 230, 210, 120, 132, 101, 134, 90],
          },
          {
            name: "净销量",
            type: "line",
            data: [50, 182, 500, 234, 290, 330, 310, 220, 182, 191, 234, 290],
          },
          {
            name: "油气均价",
            type: "line",
            data: [80, 232, 201, 154, 190, 330, 410, 150, 232, 201, 154, 190],
          },
          {
            name: "利润总额",
            type: "line",
            data: [350, 332, 301, 234, 390, 330, 150, 320, 332, 301, 334, 390],
          },
        ],
      };
      mychart.setOption(this.option);
    },
  },
};
</script>
<style lang="scss" scoped>
.oil-cost {
  width: 100%;
  .chart-box {
    width: 100%;
    height: 240px;
  }
}
</style>
