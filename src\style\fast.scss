/*common margin*/
.margin_right_10 {
  margin-right: 10px;
}
.margin_right_80 {
  margin-right: 80px;
}
.margin_left_10 {
  margin-left: 10px;
}

.margin_top_10 {
  margin-top: 10px;
}

.margin_bottom_10 {
  margin-bottom: 10px;
}

.padding_top20{
  padding-top: 20px;
}
.padding_bottom20{
  padding-bottom: 20px;
}
.padding_left20{
  padding-left: 20px;
}
.padding_right20{
  padding-right: 20px;
}

.dis_block {
  display: block;
}

.flex-row-start-start {
  display: flex;
}

.flex-row-start-center {
  display: flex;
  align-items: center;
}

.flex-row-start-end {
  display: flex;
  align-items: flex-end;
}

.flex-row-center-start {
  display: flex;
  justify-content: center;
}

.flex-row-center-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-row-center-end {
  display: flex;
  justify-content: center;
  align-items: flex-end;
}

.flex-row-end-start {
  display: flex;
  justify-content: flex-end;
}

.flex-row-end-center {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.flex-row-end-end {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
}

.width_100 {
  width: 100%;
}

.flex-row-between-start {
  display: flex;
  justify-content: space-between;
}

.flex-row-between-center {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-row-between-end {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.flex-row-around-start {
  display: flex;
  justify-content: space-around;
}

.flex-row-around-center {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.flex-row-around-end {
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
}

.flex-column-start-start {
  display: flex;
  flex-direction: column;
}

.flex-column-start-center {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.flex-column-start-end {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.flex-column-center-start {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.flex-column-center-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.flex-column-center-end {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
}

.flex-column-end-start {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.flex-column-end-center {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
}

.flex-column-end-end {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-end;
}

.flex-column-between-start {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.flex-column-between-center {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

.flex-column-between-end {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-end;
}

.flex-column-around-start {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.flex-column-around-center {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
}

.flex-column-around-end {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: flex-end;
}
.font_bold{
  font-weight: bold;
}
.font_color_FFF{
  color: #ffffff;
}
.margin_right_1rem {
  margin-right: 1rem;
}
.margin_left_1rem {
  margin-left: 1rem;
}
