<template>
    <div class="table_wrap">
        <el-table :data="tableData"
                  v-loading="loading"
                  style="width: 100%"
                  :row-class-name="tableRowClassName"
                  @selection-change="handleSelectionChange"
                  :row-key="(row)=>{ return row.id }"
                  ref="TableView"
                  tooltip-effect="dark"
                  class="resetTableStyle"
                  :height="height">
            <slot name="checkbox"></slot>
            <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
            <el-table-column
                    v-for="item in tableEle"
                    :show-overflow-tooltip="item.showover"
                    :key="item.label"
                    :prop="item.prop"
                    :label="item.label"
                    :width="item.width"
                    :fixed="item.fixed"
                    :align="item.align">
                <template slot-scope="scope">
                    <div>
                        <span v-if="item.custom">
                            <slot :data="{ prop:item.prop,data:scope.row,index: scope.$index}" :name="item.prop"></slot>
                        </span>
                        <span v-else>{{ scope.row[item.prop]}}</span>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <div class="note">
            <slot name="note"></slot>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'TableView',
        props: {
            tableData: {
                type: Array,
            },
            tableEle: {
                type: Array,
            },
            height: {
                type: String,
            },
            loading: {
                type: Boolean,
            },
            showIndex: {
                type: Boolean,
                default: () => {
                    return true;
                },
            },
            tableRowClassName: {
                type: Function,
                default: () => {
                    return () => {
                    };
                },
            },

        },
        mounted() {

        },
        data() {
            return {}
        },
        methods: {
            handleSelectionChange(val) {
                this.$emit('SelectionChange', val)
            },
            //操作按钮点击事件
            appClick(methodsName, item) {
                this.$emit(methodsName, item)
            },
        }
    }

</script>

<style scoped lang="less">
    .table_wrap {
        font-size: 14px !important;

        .note {
            display: flex;
            justify-content: flex-start;
        }
    }
</style>
