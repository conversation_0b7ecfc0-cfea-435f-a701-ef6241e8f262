import './public-path' // 注意需要引入public-path
import Vue from 'vue'
import App from './App.vue'
import {router} from './router';
import store from './store'
import VueRouter from 'vue-router'
import '@/assets/iconfont/iconfont.css'
import cfbgcRem from 'cfbgc-rem/lib'

import 'cfbgc-design/dist/cfbgc-ui.css';
import Cfbgcui from 'cfbgc-design';

import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';


Vue.use(Cfbgcui)
Vue.use(ElementUI)

Vue.use(VueRouter)
Vue.config.productionTip = false
let instance = null
window.projectName = 'micro_vue';


function render(props = {}) {
  // 这个是我们在父类注册的时候定义的那些参数。
  const {container, routerBase} = props
  const routerInstantce = router(window.__POWERED_BY_QIANKUN__ ? routerBase : process.env.BASE_URL)

  let options = {min: {width: 1366 * 0.8, height: 691 * 0.8}, html: false}
  options = container ? {
      ...options,
      element: container.querySelector(`#app`),
      html: false
    } : {
      ...options,
      elementName: '#app',
      html: true
    }
    Vue.use(cfbgcRem, options)

  instance = new Vue({
    router: routerInstantce,
    store,
    render: (h) => h(App)
  }).$mount(container ? container.querySelector('#app') : '#app')
}

if (!window.__POWERED_BY_QIANKUN__) {
  render()
}

export async function bootstrap() {
  // console.log('[vue] vue app bootstraped')
}

export async function mount(props) {
  // console.log('[vue] props from main framework', props)
  props.onGlobalStateChange((state, prev) => {
      // state: 变更后的状态; prev 变更前的状态
      // console.log('监听主题变化', state, prev)
      let preTheme = prev.themeColor
      let curTheme = state.themeColor
      console.log('主题变量=======', preTheme, curTheme)
      // 切换主题
      store.commit('changeTheme', curTheme)
  })
  render(props)
}

export async function unmount() {
  instance.$destroy()
  instance.$el.innerHTML = ''
  instance = null
}


// 引入公共组件
import chartBox from '@/components/common/chartBox.vue'
import chartCard from '@/components/common/chartCard.vue'
import header from '@/components/common/header.vue'
import dialogBox from '@/components/common/dialogBox.vue'
import fileUpload from '@/components/common/fileUploader.vue'

const components = [
  {name: 'ChartBox', component: chartBox},
  {name: 'ChartCard', component: chartCard},
  {name: 'Header', component: header},
  {name: 'DialogBox', component: dialogBox},
  {name: 'FileUpload', component: fileUpload},
]
components.forEach(item => {
  Vue.component(item.name, item.component)
})