const downFileUtil = (pdfData, fileName = "file.pdf") => {
  // 将二进制数据转换为Blob对象
  const blob = new Blob([pdfData], { type: "application/pdf" });

  // 创建下载链接
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = fileName;

  // 触发点击下载
  document.body.appendChild(a);
  a.click();

  // 清理
  window.URL.revokeObjectURL(url);
  document.body.removeChild(a);
};
export { downFileUtil };
