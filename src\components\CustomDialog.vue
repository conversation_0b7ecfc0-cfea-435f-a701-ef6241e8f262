<template>
    <div class="dialog_wrap" >
        <div class="content" >
            <div class="header">
                <div class="title">
                    <slot name="title"></slot>
                </div>
                <div class="close" style="" @click="closeDia()">
                    <img  src="@/assets/images/关闭.png" style="width: 30px;height: 30px" @click="closeDia()">
                </div>
            </div>
            <div class="body">
                <slot name="body"></slot>
            </div>
            <div class="footer">
                <slot name="footer"></slot>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        name: "CustomDialog",
        data() {
            return {
            }
        },
        computed: {},
        // 监控 data 中的数据变化
        watch: {},
        // 方法集合
        methods: {
            closeDia() {
                this.$emit('close')
            },
        },
        // 生命周期 - 创建完成（可以访问当前this 实例）
        created() {
        },
        // 生命周期 - 挂载完成（可以访问 DOM 元素）
        mounted() {
        },
        beforeCreate() {
        },
        beforeMount() {
        }, // 生命周期 - 挂载之前
        beforeUpdate() {
        }, // 生命周期 - 更新之前
        updated() {
        }, // 生命周期 - 更新之后
        beforeDestroy() {
        }, // 生命周期 - 销毁之前
        destroyed() {
        }, // 生命周期 - 销毁完成
        activated() {
        } // 如果页面有 keep-alive 缓存功能,这个函数会触发
    }
</script>

<style scoped lang="scss">
    .dialog_wrap {
        position: fixed;
        top: 0;
        right: 0;
        z-index: 10;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        .content {
            .header {
                display: flex;
                justify-content: center;
                align-items: center;
                position: relative;
                .title {
                    font-size: 1rem;
                    font-family: PingFangSC-Semibold, PingFang SC;
                    font-weight: 600;
                    color: orange;
                }

                .close {
                    position: absolute;
                    right: 0;
                    width: 3rem;
                    height: 3rem;
                    top: 0;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }
            .body {
                max-height: calc(100vh - 7.8rem);
                overflow-y: auto;
            }

            .footer {
                padding: 10px;
                display: flex;
                justify-content: center;
                align-items: center;
            }

        }
    }

</style>
