# SmartTable 组件使用文档

## 目录

- [1. 组件概述](#1-组件概述)
- [2. 安装和导入](#2-安装和导入)
  - [2.1 组件引入](#21-组件引入)
  - [2.2 基本模板结构](#22-基本模板结构)
- [3. 基本用法](#3-基本用法)
  - [3.1 最简单的使用示例](#31-最简单的使用示例)
  - [3.2 带分页的表格](#32-带分页的表格)
- [4. API 参考](#4-api-参考)
  - [4.1 Props 属性](#41-props-属性)
  - [4.2 列配置 (Column) 属性](#42-列配置-column-属性)
  - [4.3 验证规则 (Validation) 配置](#43-验证规则-validation-配置)
  - [4.4 汇总配置 (SummaryConfig) 属性](#44-汇总配置-summaryconfig-属性)
  - [4.5 事件](#45-事件)
  - [4.6 插槽](#46-插槽)
- [5. 高级功能](#5-高级功能)
  - [5.1 多级表头配置](#51-多级表头配置)
  - [5.2 单元格编辑配置](#52-单元格编辑配置)
  - [5.3 数据汇总配置](#53-数据汇总配置)
  - [5.4 单元格合并配置](#54-单元格合并配置)
- [6. 样式定制](#6-样式定制)
  - [6.1 主题切换](#61-主题切换)
  - [6.2 自定义样式类](#62-自定义样式类)
- [7. 实际示例](#7-实际示例)
  - [7.1 完整的生产计划表格示例](#71-完整的生产计划表格示例)
  - [7.2 带验证的表单表格示例](#72-带验证的表单表格示例)
  - [7.3 复杂汇总统计示例](#73-复杂汇总统计示例)
  - [7.4 动态列配置示例](#74-动态列配置示例)
- [8. 常见问题](#8-常见问题)
- [9. 最佳实践](#9-最佳实践)
  - [9.1 性能优化](#91-性能优化)
  - [9.2 数据验证](#92-数据验证)
  - [9.3 用户体验](#93-用户体验)
  - [9.4 代码组织](#94-代码组织)
- [附录](#附录)
  - [A. 完整的类型定义](#a-完整的类型定义)
  - [B. 常用配置模板](#b-常用配置模板)

## 1. 组件概述

SmartTable（OgwTable）是一个基于 Element UI 的高级表格组件，提供了丰富的功能特性，包括：

- **数据展示**：支持复杂的表格数据展示，包括多级表头、单元格合并
- **在线编辑**：支持单元格内容的在线编辑，包括文本、数字、日期、下拉选择等
- **数据验证**：内置多种数据验证规则，确保数据的准确性
- **汇总统计**：支持分组汇总、小计、总计等统计功能
- **分页功能**：内置分页组件，支持大数据量展示
- **主题适配**：支持浅色和深色主题切换
- **操作列**：可配置的操作列，支持自定义操作按钮

## 2. 安装和导入

### 2.1 组件引入

```javascript
import OgwTable from "@/components/comTable/OgwTable.vue";

export default {
  components: {
    OgwTable,
  },
  // ...
};
```

### 2.2 基本模板结构

```vue
<template>
  <div>
    <OgwTable
      :columns="columns"
      :data="tableData"
      :loading="loading"
      @cell-change="handleCellChange"
    />
  </div>
</template>
```

## 3. 基本用法

### 3.1 最简单的使用示例

```vue
<template>
  <div>
    <OgwTable
      :columns="columns"
      :data="tableData"
    />
  </div>
</template>

<script>
import OgwTable from "@/components/comTable/OgwTable.vue";

export default {
  components: {
    OgwTable,
  },
  data() {
    return {
      columns: [
        { label: "姓名", prop: "name" },
        { label: "年龄", prop: "age" },
        { label: "职位", prop: "position" },
      ],
      tableData: [
        { name: "张三", age: 25, position: "前端工程师" },
        { name: "李四", age: 30, position: "后端工程师" },
        { name: "王五", age: 28, position: "产品经理" },
      ],
    };
  },
};
</script>
```

### 3.2 带分页的表格

```vue
<template>
  <div>
    <OgwTable
      :columns="columns"
      :data="tableData"
      :pagination="true"
      :page-size="10"
      :current-page="currentPage"
      :total="total"
      @page-change="handlePageChange"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      currentPage: 1,
      total: 100,
      // ... 其他数据
    };
  },
  methods: {
    handlePageChange({ pageSize, currentPage }) {
      this.currentPage = currentPage;
      // 重新加载数据
      this.loadData();
    },
  },
};
</script>
```

## 4. API 参考

### 4.1 Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| data | Array | - | 表格数据 |
| columns | Array | [] | 列配置数组 |
| loading | Boolean | false | 是否显示加载状态 |
| showIndex | Boolean | false | 是否显示序号列 |
| showActions | Boolean | false | 是否显示操作列 |
| actionColumnWidth | Number/String | - | 操作列宽度 |
| pagination | Boolean | false | 是否启用分页 |
| pageSize | Number | 10 | 每页显示条数 |
| currentPage | Number | 1 | 当前页码 |
| total | Number | 0 | 总条数 |
| pageSizes | Array | [10, 20, 50, 100] | 每页显示个数选择器的选项 |
| mergeKeys | Array | [] | 需要合并的列字段名数组 |
| summaryConfig | Object | {} | 汇总配置对象 |

### 4.2 列配置 (Column) 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| label | String | - | 列标题 |
| prop | String | - | 对应数据字段名 |
| width | Number/String | - | 列宽度 |
| minWidth | Number | 120 | 最小列宽度 |
| align | String | 'center' | 对齐方式 |
| fixed | String | - | 列固定位置 ('left'/'right') |
| editable | Boolean | false | 是否可编辑 |
| clickable | Boolean | false | 是否可点击 |
| children | Array | - | 子列配置（多级表头） |
| formatter | Function | - | 格式化函数 |
| options | Array | - | 下拉选项（编辑时） |
| editType | String | - | 编辑类型 ('date') |
| editComponent | String | 'el-input' | 编辑组件 |
| validation | Object | - | 验证规则配置 |
| dateConfig | Object | - | 日期配置 |

### 4.3 验证规则 (Validation) 配置

| 属性名 | 类型 | 说明 |
|--------|------|------|
| type | String | 验证类型：'text'/'number'/'decimal'/'regex' |
| required | Boolean | 是否必填 |
| min | Number | 最小值（数字类型） |
| max | Number | 最大值（数字类型） |
| precision | Number | 小数位数（decimal类型） |
| pattern | String | 正则表达式（regex类型） |
| minLength | Number | 最小长度（text类型） |
| maxLength | Number | 最大长度（text类型） |
| errorMessage | String | 自定义错误信息 |

### 4.4 汇总配置 (SummaryConfig) 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| groupField | String | '' | 分组字段 |
| sumColumns | Array | [] | 需要求和的列 |
| showSubTotal | Boolean | false | 是否显示小计 |
| showGrandTotal | Boolean | false | 是否显示总计 |
| subTotalText | String | '小计' | 小计文本 |
| grandTotalText | String | '合计' | 总计文本 |
| subTotalTextMergeColumns | Array | [] | 小计文本合并的列 |
| grandTotalTextMergeColumns | Array | [] | 总计文本合并的列 |
| summaryRowClass | String | 'summary-row' | 汇总行样式类名 |

### 4.5 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| cell-change | { row, prop, index, value } | 单元格值改变时触发 |
| cell-click | { row, prop, index, value } | 单元格点击时触发 |
| page-change | { pageSize, currentPage } | 分页改变时触发 |
| edit | row | 编辑按钮点击时触发 |
| delete | row | 删除按钮点击时触发 |

### 4.6 插槽

| 插槽名 | 说明 | 作用域参数 |
|--------|------|-----------|
| actions | 操作列内容 | { row, $index } |
| [prop] | 自定义列内容 | { row, $index } |

## 5. 高级功能

### 5.1 多级表头配置

```javascript
columns: [
  { label: "基本信息", prop: "basic" },
  {
    label: "累计产量",
    children: [
      { label: "实际产量", prop: "actualAmount" },
      { label: "计划产量", prop: "planAmount" },
      { label: "完成率", prop: "completionRate" },
    ],
  },
]
```

### 5.2 单元格编辑配置

```javascript
columns: [
  {
    label: "数量",
    prop: "quantity",
    editable: true,
    validation: {
      type: "number",
      required: true,
      min: 0,
      max: 1000,
      errorMessage: "请输入0-1000之间的数字",
    },
  },
  {
    label: "类型",
    prop: "type",
    editable: true,
    options: [
      { label: "类型A", value: "A" },
      { label: "类型B", value: "B" },
    ],
  },
  {
    label: "日期",
    prop: "date",
    editable: true,
    editType: "date",
    dateConfig: {
      type: "date",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd",
    },
  },
]
```

### 5.3 数据汇总配置

```javascript
summaryConfig: {
  groupField: "category", // 按类别分组
  sumColumns: ["amount", "quantity"], // 对金额和数量求和
  showSubTotal: true, // 显示小计
  showGrandTotal: true, // 显示总计
  subTotalText: "小计",
  grandTotalText: "合计",
  subTotalTextMergeColumns: ["name"], // 小计时合并名称列
  grandTotalTextMergeColumns: ["category", "name"], // 总计时合并类别和名称列
}
```

### 5.4 单元格合并配置

```javascript
// 合并相同值的单元格
mergeKeys: ["category", "type"]
```

## 6. 样式定制

### 6.1 主题切换

组件支持浅色和深色主题，通过设置 `data-theme` 属性来切换：

```javascript
// 设置浅色主题
document.documentElement.setAttribute("data-theme", "tint");

// 设置深色主题
document.documentElement.setAttribute("data-theme", "dark");
```

### 6.2 自定义样式类

```css
/* 自定义汇总行样式 */
.custom-summary-row {
  background-color: #f5f7fa;
  font-weight: bold;
}

/* 自定义可编辑单元格样式 */
.custom-editable-cell {
  border: 1px dashed #409eff;
}
```

## 7. 实际示例

### 7.1 完整的生产计划表格示例

```vue
<template>
  <div class="production-plan">
    <OgwTable
      :columns="columns"
      :data="tableData"
      :merge-keys="['planYear', 'deviceNameType']"
      :summary-config="summaryConfig"
      :pagination="true"
      :page-size="pageSize"
      :current-page="currentPage"
      :total="total"
      :loading="loading"
      :show-index="true"
      :show-actions="true"
      @cell-change="handleCellChange"
      @cell-click="handleCellClick"
      @page-change="handlePageChange"
    >
      <template #actions="{ row }">
        <el-button type="text" size="small" @click="editRow(row)">
          编辑
        </el-button>
        <el-button type="text" size="small" @click="deleteRow(row)">
          删除
        </el-button>
      </template>
    </OgwTable>
  </div>
</template>

<script>
import OgwTable from "@/components/comTable/OgwTable.vue";

export default {
  name: "ProductionPlan",
  components: {
    OgwTable,
  },
  data() {
    return {
      loading: false,
      pageSize: 10,
      currentPage: 1,
      total: 0,
      columns: [
        { label: "计划年份", prop: "planYear" },
        { label: "装置平台", prop: "deviceNameType" },
        { label: "产品类型", prop: "productType" },
        {
          label: "单位",
          prop: "unit",
          editable: true,
          validation: {
            type: "text",
            required: true,
            maxLength: 10,
          },
        },
        {
          label: "一月",
          children: [
            {
              label: "计划量",
              prop: "planAmount1",
              editable: true,
              validation: {
                type: "decimal",
                precision: 2,
                min: 0,
              },
            },
            {
              label: "实际量",
              prop: "realAmount1",
              editable: true,
              validation: {
                type: "decimal",
                precision: 2,
                min: 0,
              },
            },
          ],
        },
        // ... 其他月份列
      ],
      summaryConfig: {
        groupField: "deviceNameType",
        sumColumns: ["planAmount1", "realAmount1"],
        showSubTotal: true,
        showGrandTotal: true,
        subTotalText: "小计",
        grandTotalText: "合计",
        subTotalTextMergeColumns: ["productType", "unit"],
        grandTotalTextMergeColumns: ["planYear", "deviceNameType", "productType", "unit"],
      },
      tableData: [
        {
          planYear: "2024",
          deviceNameType: "深海一号",
          productType: "天然气",
          unit: "亿立方米",
          planAmount1: 100.5,
          realAmount1: 95.2,
        },
        // ... 更多数据
      ],
    };
  },
  methods: {
    handleCellChange({ row, prop, index, value }) {
      console.log(`第${index + 1}行的${prop}字段值改变为:`, value);
      // 保存数据到后端
      this.saveData(row);
    },
    handleCellClick({ row, prop, index, value }) {
      console.log(`点击了第${index + 1}行的${prop}字段，值为:`, value);
      // 处理单元格点击事件
    },
    handlePageChange({ pageSize, currentPage }) {
      this.pageSize = pageSize;
      this.currentPage = currentPage;
      this.loadData();
    },
    editRow(row) {
      // 编辑行数据
      console.log("编辑行:", row);
    },
    deleteRow(row) {
      // 删除行数据
      console.log("删除行:", row);
    },
    async saveData(row) {
      try {
        // 调用API保存数据
        await this.$api.saveProductionPlan(row);
        this.$message.success("保存成功");
      } catch (error) {
        this.$message.error("保存失败");
      }
    },
    async loadData() {
      this.loading = true;
      try {
        const response = await this.$api.getProductionPlan({
          page: this.currentPage,
          size: this.pageSize,
        });
        this.tableData = response.data;
        this.total = response.total;
      } catch (error) {
        this.$message.error("加载数据失败");
      } finally {
        this.loading = false;
      }
    },
  },
  mounted() {
    this.loadData();
  },
};
</script>
```

## 8. 常见问题

### 8.1 编辑状态无法退出

**问题**：点击单元格进入编辑状态后，无法正常退出编辑。

**解决方案**：
- 确保验证规则配置正确
- 检查是否有阻止事件冒泡的代码
- 验证失败时会自动回滚，需要修正数据后才能退出

### 8.2 汇总数据不正确

**问题**：配置了汇总功能，但计算结果不正确。

**解决方案**：
- 检查 `sumColumns` 配置是否正确
- 确保数据字段为数字类型
- 验证 `groupField` 字段是否存在

### 8.3 分页数据不更新

**问题**：切换页码后，表格数据没有更新。

**解决方案**：
- 确保监听了 `page-change` 事件
- 在事件处理函数中重新加载数据
- 检查 `total` 属性是否正确设置

### 8.4 主题样式不生效

**问题**：切换主题后，表格样式没有改变。

**解决方案**：
- 确保正确设置了 `data-theme` 属性
- 检查CSS样式是否正确引入
- 验证主题属性值是否正确（'default'/'tint'/'dark'）

## 9. 最佳实践

### 9.1 性能优化

1. **大数据量处理**：启用分页功能，避免一次性渲染过多数据
2. **列配置优化**：合理设置列宽度，避免频繁的列宽计算
3. **事件处理**：在事件处理函数中使用防抖或节流

### 9.2 数据验证

1. **前端验证**：使用内置验证规则进行前端数据校验
2. **后端验证**：前端验证通过后，仍需在后端进行数据验证
3. **错误提示**：提供清晰的错误提示信息

### 9.3 用户体验

1. **加载状态**：在数据加载时显示loading状态
2. **操作反馈**：提供明确的操作成功/失败反馈
3. **键盘操作**：支持Tab键切换编辑单元格，Enter键确认编辑

### 9.4 代码组织

1. **组件拆分**：将复杂的列配置提取为单独的配置文件
2. **事件处理**：将事件处理逻辑封装为独立的方法
3. **数据管理**：使用Vuex或其他状态管理工具管理表格数据

### 7.2 带验证的表单表格示例

```vue
<template>
  <div class="form-table">
    <OgwTable
      :columns="columns"
      :data="tableData"
      :loading="loading"
      @cell-change="handleCellChange"
    />
    <div class="table-actions">
      <el-button type="primary" @click="saveAll">保存全部</el-button>
      <el-button @click="resetAll">重置</el-button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      columns: [
        {
          label: "员工姓名",
          prop: "name",
          editable: true,
          validation: {
            type: "text",
            required: true,
            minLength: 2,
            maxLength: 10,
            errorMessage: "姓名长度必须在2-10个字符之间",
          },
        },
        {
          label: "年龄",
          prop: "age",
          editable: true,
          validation: {
            type: "number",
            required: true,
            min: 18,
            max: 65,
            errorMessage: "年龄必须在18-65岁之间",
          },
        },
        {
          label: "薪资",
          prop: "salary",
          editable: true,
          validation: {
            type: "decimal",
            required: true,
            precision: 2,
            min: 3000,
            errorMessage: "薪资不能低于3000元",
          },
          formatter: (row, value) => `¥${value}`,
        },
        {
          label: "部门",
          prop: "department",
          editable: true,
          options: [
            { label: "技术部", value: "tech" },
            { label: "产品部", value: "product" },
            { label: "运营部", value: "operation" },
          ],
        },
        {
          label: "入职日期",
          prop: "joinDate",
          editable: true,
          editType: "date",
          dateConfig: {
            type: "date",
            format: "yyyy年MM月dd日",
            valueFormat: "yyyy-MM-dd",
            placeholder: "请选择入职日期",
          },
        },
        {
          label: "邮箱",
          prop: "email",
          editable: true,
          validation: {
            type: "regex",
            pattern: "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$",
            errorMessage: "请输入有效的邮箱地址",
          },
        },
      ],
      tableData: [
        {
          name: "张三",
          age: 25,
          salary: 8000,
          department: "tech",
          joinDate: "2023-01-15",
          email: "<EMAIL>",
        },
        // ... 更多数据
      ],
    };
  },
  methods: {
    handleCellChange({ row, prop, index, value }) {
      // 实时保存或标记为已修改
      row._modified = true;
      this.validateRow(row);
    },
    validateRow(row) {
      // 验证整行数据
      const errors = [];
      this.columns.forEach(col => {
        if (col.validation) {
          const validation = this.validateField(row[col.prop], col.validation);
          if (!validation.valid) {
            errors.push(`${col.label}: ${validation.message}`);
          }
        }
      });
      row._errors = errors;
      return errors.length === 0;
    },
    saveAll() {
      const validRows = this.tableData.filter(row => this.validateRow(row));
      if (validRows.length === this.tableData.length) {
        // 所有数据验证通过，保存到后端
        this.submitData(this.tableData);
      } else {
        this.$message.error("请修正表格中的错误数据");
      }
    },
  },
};
</script>
```

### 7.3 复杂汇总统计示例

```vue
<template>
  <div class="statistics-table">
    <OgwTable
      :columns="columns"
      :data="tableData"
      :merge-keys="['region', 'product']"
      :summary-config="summaryConfig"
      :show-index="true"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      columns: [
        { label: "地区", prop: "region" },
        { label: "产品", prop: "product" },
        { label: "销售额", prop: "sales", formatter: (row, value) => `¥${value.toLocaleString()}` },
        { label: "数量", prop: "quantity" },
        { label: "利润", prop: "profit", formatter: (row, value) => `¥${value.toLocaleString()}` },
      ],
      summaryConfig: {
        groupField: "region", // 按地区分组
        sumColumns: ["sales", "quantity", "profit"], // 对销售额、数量、利润求和
        showSubTotal: true,
        showGrandTotal: true,
        showCustomGrandTotal: true,
        subTotalText: "地区小计",
        grandTotalText: "总计",
        customGrandTotalText: "平均值",
        subTotalTextMergeColumns: ["product"],
        grandTotalTextMergeColumns: ["region", "product"],
        customGrandTotalTextMergeColumns: ["region", "product"],
        // 自定义总计计算函数
        customGrandTotalData: (data) => {
          const totalSales = data.reduce((sum, row) => sum + row.sales, 0);
          const totalQuantity = data.reduce((sum, row) => sum + row.quantity, 0);
          const totalProfit = data.reduce((sum, row) => sum + row.profit, 0);
          const count = data.length;

          return {
            sales: Math.round(totalSales / count),
            quantity: Math.round(totalQuantity / count),
            profit: Math.round(totalProfit / count),
          };
        },
      },
      tableData: [
        { region: "华北", product: "产品A", sales: 100000, quantity: 500, profit: 20000 },
        { region: "华北", product: "产品B", sales: 150000, quantity: 300, profit: 30000 },
        { region: "华南", product: "产品A", sales: 120000, quantity: 600, profit: 25000 },
        { region: "华南", product: "产品B", sales: 180000, quantity: 400, profit: 35000 },
      ],
    };
  },
};
</script>
```

### 7.4 动态列配置示例

```vue
<template>
  <div class="dynamic-table">
    <div class="table-controls">
      <el-button @click="addMonth">添加月份</el-button>
      <el-button @click="removeMonth">移除月份</el-button>
    </div>
    <OgwTable
      :columns="dynamicColumns"
      :data="tableData"
      :loading="loading"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      monthCount: 3,
      baseColumns: [
        { label: "产品名称", prop: "productName" },
        { label: "单位", prop: "unit" },
      ],
      tableData: [
        {
          productName: "产品A",
          unit: "吨",
          month1: 100,
          month2: 120,
          month3: 110,
        },
      ],
    };
  },
  computed: {
    dynamicColumns() {
      const monthColumns = [];
      for (let i = 1; i <= this.monthCount; i++) {
        monthColumns.push({
          label: `${i}月`,
          children: [
            {
              label: "计划",
              prop: `month${i}Plan`,
              editable: true,
              validation: { type: "number", min: 0 },
            },
            {
              label: "实际",
              prop: `month${i}Actual`,
              editable: true,
              validation: { type: "number", min: 0 },
            },
          ],
        });
      }
      return [...this.baseColumns, ...monthColumns];
    },
  },
  methods: {
    addMonth() {
      if (this.monthCount < 12) {
        this.monthCount++;
        // 为现有数据添加新月份字段
        this.tableData.forEach(row => {
          row[`month${this.monthCount}Plan`] = 0;
          row[`month${this.monthCount}Actual`] = 0;
        });
      }
    },
    removeMonth() {
      if (this.monthCount > 1) {
        // 移除最后一个月份的数据
        this.tableData.forEach(row => {
          delete row[`month${this.monthCount}Plan`];
          delete row[`month${this.monthCount}Actual`];
        });
        this.monthCount--;
      }
    },
  },
};
</script>
```

## 8. 常见问题

### 8.1 编辑状态无法退出

**问题**：点击单元格进入编辑状态后，无法正常退出编辑。

**解决方案**：
- 确保验证规则配置正确
- 检查是否有阻止事件冒泡的代码
- 验证失败时会自动回滚，需要修正数据后才能退出

### 8.2 汇总数据不正确

**问题**：配置了汇总功能，但计算结果不正确。

**解决方案**：
- 检查 `sumColumns` 配置是否正确
- 确保数据字段为数字类型
- 验证 `groupField` 字段是否存在

### 8.3 分页数据不更新

**问题**：切换页码后，表格数据没有更新。

**解决方案**：
- 确保监听了 `page-change` 事件
- 在事件处理函数中重新加载数据
- 检查 `total` 属性是否正确设置

### 8.4 主题样式不生效

**问题**：切换主题后，表格样式没有改变。

**解决方案**：
- 确保正确设置了 `data-theme` 属性
- 检查CSS样式是否正确引入
- 验证主题属性值是否正确（'default'/'tint'/'dark'）

### 8.5 单元格合并异常

**问题**：配置了 `mergeKeys` 后，单元格合并效果不正确。

**解决方案**：
- 确保数据按合并字段排序
- 检查合并字段的值是否完全相同（包括数据类型）
- 验证 `mergeKeys` 数组中的字段名是否正确

### 8.6 日期编辑器显示异常

**问题**：日期编辑器弹出层位置不正确或无法显示。

**解决方案**：
- 确保设置了 `append-to-body="true"`
- 检查CSS z-index层级设置
- 验证日期格式配置是否正确

### 8.7 验证规则不生效

**问题**：配置了验证规则，但输入错误数据时没有提示。

**解决方案**：
- 检查验证规则配置语法是否正确
- 确保 `validation` 对象的属性名正确
- 验证正则表达式是否有效（regex类型）

### 8.8 性能问题

**问题**：表格数据量大时，页面卡顿或响应慢。

**解决方案**：
- 启用分页功能，减少单页数据量
- 避免在 `formatter` 函数中进行复杂计算
- 使用 `v-show` 替代 `v-if` 来控制列显示
- 考虑使用虚拟滚动（需要额外实现）

## 9. 最佳实践

### 9.1 性能优化

1. **大数据量处理**：
   ```javascript
   // 推荐：使用分页
   <OgwTable
     :pagination="true"
     :page-size="50"
     :data="currentPageData"
   />

   // 避免：一次性加载所有数据
   <OgwTable :data="allData" /> // allData.length > 1000
   ```

2. **列配置优化**：
   ```javascript
   // 推荐：合理设置列宽
   columns: [
     { label: "ID", prop: "id", width: 80 },
     { label: "名称", prop: "name", minWidth: 120 },
     { label: "描述", prop: "desc" }, // 自适应宽度
   ]

   // 避免：所有列都使用固定宽度
   columns: [
     { label: "ID", prop: "id", width: 200 }, // 过宽
     { label: "名称", prop: "name", width: 100 }, // 过窄
   ]
   ```

3. **事件处理优化**：
   ```javascript
   // 推荐：使用防抖处理频繁事件
   import { debounce } from 'lodash';

   methods: {
     handleCellChange: debounce(function({ row, prop, index, value }) {
       this.saveData(row);
     }, 500),
   }

   // 避免：每次变化都立即处理
   handleCellChange({ row, prop, index, value }) {
     this.saveData(row); // 可能导致频繁请求
   }
   ```

### 9.2 数据验证

1. **前端验证**：
   ```javascript
   // 推荐：完整的验证配置
   validation: {
     type: "decimal",
     required: true,
     precision: 2,
     min: 0,
     max: 999999.99,
     errorMessage: "请输入有效的金额（0-999999.99）",
   }

   // 避免：不完整的验证
   validation: {
     type: "decimal", // 缺少其他约束
   }
   ```

2. **后端验证**：
   ```javascript
   // 推荐：前后端双重验证
   async saveData(row) {
     try {
       // 前端验证通过后，后端再次验证
       await this.$api.saveData(row);
       this.$message.success("保存成功");
     } catch (error) {
       if (error.code === 'VALIDATION_ERROR') {
         this.$message.error(error.message);
       }
     }
   }
   ```

### 9.3 用户体验

1. **加载状态**：
   ```javascript
   // 推荐：明确的加载状态
   async loadData() {
     this.loading = true;
     try {
       const data = await this.$api.getData();
       this.tableData = data;
     } finally {
       this.loading = false; // 确保loading状态被清除
     }
   }
   ```

2. **操作反馈**：
   ```javascript
   // 推荐：清晰的操作反馈
   async handleCellChange({ row, prop, index, value }) {
     try {
       await this.saveData(row);
       this.$message({
         message: `${prop}字段保存成功`,
         type: 'success',
         duration: 2000,
       });
     } catch (error) {
       this.$message.error(`保存失败：${error.message}`);
       // 回滚数据
       this.reloadRowData(row);
     }
   }
   ```

3. **键盘操作支持**：
   ```javascript
   // 推荐：支持键盘导航
   mounted() {
     // 监听键盘事件
     document.addEventListener('keydown', this.handleKeydown);
   },
   beforeDestroy() {
     document.removeEventListener('keydown', this.handleKeydown);
   },
   methods: {
     handleKeydown(event) {
       if (event.key === 'Tab') {
         // 处理Tab键切换编辑单元格
         this.moveToNextEditableCell();
       }
     },
   }
   ```

### 9.4 代码组织

1. **组件拆分**：
   ```javascript
   // 推荐：将配置提取为单独文件
   // tableConfig.js
   export const productionColumns = [
     { label: "产品名称", prop: "name" },
     // ... 更多列配置
   ];

   export const productionSummaryConfig = {
     groupField: "category",
     sumColumns: ["amount"],
     // ... 更多配置
   };

   // 组件中引入
   import { productionColumns, productionSummaryConfig } from './tableConfig';
   ```

2. **事件处理封装**：
   ```javascript
   // 推荐：封装通用的事件处理逻辑
   // mixins/tableMixin.js
   export default {
     methods: {
       async handleTableCellChange({ row, prop, index, value }) {
         // 通用的单元格变化处理逻辑
         row._modified = true;
         await this.validateAndSave(row, prop, value);
       },
       async validateAndSave(row, prop, value) {
         // 通用的验证和保存逻辑
       },
     },
   };
   ```

3. **状态管理**：
   ```javascript
   // 推荐：使用Vuex管理复杂的表格状态
   // store/modules/table.js
   export default {
     namespaced: true,
     state: {
       tableData: [],
       loading: false,
       currentPage: 1,
       pageSize: 10,
       total: 0,
     },
     mutations: {
       SET_TABLE_DATA(state, data) {
         state.tableData = data;
       },
       SET_LOADING(state, loading) {
         state.loading = loading;
       },
       UPDATE_CELL_VALUE(state, { index, prop, value }) {
         state.tableData[index][prop] = value;
       },
     },
     actions: {
       async loadTableData({ commit }, params) {
         commit('SET_LOADING', true);
         try {
           const response = await api.getTableData(params);
           commit('SET_TABLE_DATA', response.data);
           commit('SET_TOTAL', response.total);
         } finally {
           commit('SET_LOADING', false);
         }
       },
     },
   };
   ```

---

**注意**：本文档基于组件的当前版本编写，如有更新请及时同步文档内容。

## 附录

### A. 完整的类型定义

```typescript
// SmartTable组件类型定义
interface SmartTableProps {
  data: Array<any>;
  columns: Array<ColumnConfig>;
  loading?: boolean;
  showIndex?: boolean;
  showActions?: boolean;
  actionColumnWidth?: number | string;
  pagination?: boolean;
  pageSize?: number;
  currentPage?: number;
  total?: number;
  pageSizes?: Array<number>;
  mergeKeys?: Array<string>;
  summaryConfig?: SummaryConfig;
}

interface ColumnConfig {
  label: string;
  prop?: string;
  width?: number | string;
  minWidth?: number;
  align?: 'left' | 'center' | 'right';
  fixed?: 'left' | 'right';
  editable?: boolean;
  clickable?: boolean;
  children?: Array<ColumnConfig>;
  formatter?: (row: any, value: any) => string;
  options?: Array<{ label: string; value: any }>;
  editType?: 'date';
  editComponent?: string;
  validation?: ValidationConfig;
  dateConfig?: DateConfig;
}

interface ValidationConfig {
  type: 'text' | 'number' | 'decimal' | 'regex';
  required?: boolean;
  min?: number;
  max?: number;
  precision?: number;
  pattern?: string;
  minLength?: number;
  maxLength?: number;
  errorMessage?: string;
}

interface DateConfig {
  type?: 'date' | 'month' | 'year';
  format?: string;
  valueFormat?: string;
  placeholder?: string;
  minDate?: string;
  maxDate?: string;
}

interface SummaryConfig {
  groupField?: string;
  sumColumns?: Array<string>;
  showSubTotal?: boolean;
  showGrandTotal?: boolean;
  showCustomSubTotal?: boolean;
  showCustomGrandTotal?: boolean;
  subTotalText?: string;
  grandTotalText?: string;
  customSubTotalText?: string;
  customGrandTotalText?: string;
  subTotalTextMergeColumns?: Array<string>;
  grandTotalTextMergeColumns?: Array<string>;
  customSubTotalTextMergeColumns?: Array<string>;
  customGrandTotalTextMergeColumns?: Array<string>;
  summaryRowClass?: string;
  customGrandTotalData?: Function | Object;
  customSubTotalData?: Function | Object;
}
```

### B. 常用配置模板

```javascript
// 基础表格配置
export const basicTableConfig = {
  columns: [
    { label: "ID", prop: "id", width: 80 },
    { label: "名称", prop: "name", minWidth: 120 },
    { label: "状态", prop: "status", width: 100 },
  ],
  pagination: true,
  pageSize: 20,
  showIndex: true,
};

// 可编辑表格配置
export const editableTableConfig = {
  columns: [
    {
      label: "名称",
      prop: "name",
      editable: true,
      validation: {
        type: "text",
        required: true,
        maxLength: 50,
      },
    },
    {
      label: "数量",
      prop: "quantity",
      editable: true,
      validation: {
        type: "number",
        required: true,
        min: 1,
      },
    },
  ],
};

// 汇总表格配置
export const summaryTableConfig = {
  summaryConfig: {
    groupField: "category",
    sumColumns: ["amount", "quantity"],
    showSubTotal: true,
    showGrandTotal: true,
    subTotalText: "小计",
    grandTotalText: "合计",
  },
  mergeKeys: ["category"],
};
```
