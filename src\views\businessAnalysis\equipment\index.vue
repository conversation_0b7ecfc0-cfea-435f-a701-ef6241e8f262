<template>
  <div class="home">
    <ChartBox :title="'预算执行'">
      <template v-slot:box-right>
        <div class="title-btn">海南分生产设施综合分析平台</div>
        <el-date-picker
          :clearable="false"
          v-model="dateVaule"
          type="year"
          placeholder="选择年"
        >
        </el-date-picker>
      </template>
      <div class="content">
        <div class="left">
          <Maintain :titleContent="{ text: '年度维修预算' }" />
          <Maintain
            :titleContent="{ text: '成本中心预算' }"
            :greenColor="true"
          />
        </div>
        <div class="right">
          <bar-chart :xData="xData" :yData="yData"></bar-chart>
          <BorderTable
            :border="true"
            :tableData="tableData"
            :colums="colums"
          ></BorderTable>
        </div>
      </div>
    </ChartBox>
    <div class="bottom">
      <ChartBox :title="'维修费用日估算'" class="daily">
        <template v-slot:box-right>
          <el-date-picker
          :clearable="false"
            class="range-picker"
            v-model="rangeValue"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </template>
        <DailyEstimate></DailyEstimate>
      </ChartBox>
      <div class="cost">
        <ChartBox :title="'设备外委维修费用排名'">
          <CostRank></CostRank>
        </ChartBox>
      </div>
    </div>
  </div>
</template>
<script>
import BarChart from "@/components/charts/bar/barChart.vue";
import Maintain from "./maintain/index.vue";
import DailyEstimate from "./dailyEstimation/index.vue";
import CostRank from "./costRank/index.vue";
import BorderTable from "@/components/comTable/borderTable.vue";
export default {
  components: {
    Maintain,
    BarChart,
    DailyEstimate,
    CostRank,
    BorderTable,
  },
  data() {
    return {
      dateVaule: "",
      rangeValue: "",
      xData: [
        "作业公司",
        "崖城13-1",
        "陵水17-2",
        "陵水25-1",
        "文昌16-2",
        "南山终端",
        "香港管线",
        "海南管线",
        "高栏支线",
      ],
      yData: ["25", "33", "32", "30", "28", "27", "26", "55", "24"],
      colums: [
        {
          label: "费用项目",
          prop: "expenseItems",
        },
        {
          label: "费用",
          prop: "expenseAmount",
        },
        {
          label: "同比",
          prop: "yearOnYear",
        },
        {
          label: "年度计划/执行(万元)(截止2025年6月)",
          children: [
            {
              label: "计划",
              prop: "plan",
            },
            {
              label: "执行",
              prop: "execution",
            },
            {
              label: "执行/计划",
              prop: "executionPlan",
            },
            {
              label: "年度完成",
              prop: "annualCompletion",
            },
          ],
        },
        {
          label: "超额完成项目",
          prop: "excessCompletion",
        },
        {
          label: "未达预期项目",
          prop: "unmetExpectations",
        },
      ],
      tableData: [
        {
          expenseItems: "维修费用",
          expenseAmount: "1000",
          yearOnYear: "10%",
          plan: "1000",
          execution: "800",
          executionPlan: "80%",
          annualCompletion: "80%",
          excessCompletion: "5个",
          unmetExpectations: "3个",
        },
      ],
    };
  },
};
</script>
<style lang="scss" scoped>
.content {
  display: flex;
  .left {
    flex: 2;
    display: flex;
  }

  .right {
    max-width: 100%;
    flex: 8;
    box-sizing: border-box;
    padding: 16px;
    display: flex;
    flex-direction: column;
  }
}

.bottom {
  box-sizing: border-box;
  display: flex;
  margin-top: 6px;
  margin-bottom: 10px;
  .daily {
    flex: 1;
    margin-right: 10px;
  }
  .cost {
    flex: 1;
  }
}

.title-btn {
  width: 206px;
  height: 32px;
  opacity: 1;
  font-family: Source Han Sans;
  font-size: 14px;
  color: #ffffff;
  background: linear-gradient(0deg, #0e79f3 0%, rgba(14, 121, 243, 0) 100%);
  box-sizing: border-box;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 8px;
}

::v-deep .el-date-editor {
  box-sizing: border-box;
  margin-right: 12px;
  .el-input__inner {
    padding: 0;
    height: 32px;
    line-height: 32px;

    padding-left: 15px;
    border: none;

    font-family: Source Han Sans;
    font-size: 14px;
    font-weight: normal;
  }
}

::v-deep .el-date-editor--daterange.el-input__inner {
  width: 280px;
}

::v-deep .el-date-editor.el-input {
  width: 120px;
}

::v-deep .el-input__prefix {
  left: 78%;
  .el-input__icon {
    line-height: 32px;
  }
}

.range-picker {
  height: 32px;
}
</style>
<style scoped>
.range-picker /deep/ .el-input__icon {
  position: absolute;
  right: 0;
}

[data-theme="dark"] .range-picker /deep/ .el-range-separator {
  color: #6ba4f4;
  line-height: 25px;
}

[data-theme="tint"] .range-picker /deep/ .el-range-separator {
  color: rgba(0, 0, 0, 0.65);
}

[data-theme="dark"] .home /deep/ .el-input__inner.el-range-input {
  background-color: #263e78 !important;
  color: #6ba4f4;
}

[data-theme="tint"] .home /deep/ .el-input__inner {
  background: #ffffff !important;
  color: rgba(0, 0, 0, 0.65);
}
</style>
