<template>
  <div class="price-sensitivity">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <h1>价格敏感性分析</h1>
        <div style="float: right; margin-top: -30px">
          选择纬度：
          <el-select v-model="modelType" placeholder="请选择">
            <el-option label="产销配比模型1" value="1"></el-option>
            <el-option label="产销配比模型2" value="2"></el-option>
          </el-select>
        </div>
      </div>

      <!-- 基础参数设定 -->
      <el-card shadow="hover" class="section-card">
        <div slot="header">
          <h2>基础参数设定</h2>
        </div>
        <el-row :gutter="20">
          <el-form
            :model="form"
            :inline="true"
            label-position="right"
            label-width="126px"
            class="paramsForm"
          >
            <el-col :span="6" v-for="item in formItems" :key="item.label">
              <el-form-item :label="item.label">
                <el-input
                  v-if="item.type === 'input'"
                  v-model="form[item.prop]"
                  placeholder="请输入"
                />
                <el-input
                  v-else-if="item.type === 'number'"
                  type="number"
                  v-model="form[item.prop]"
                  placeholder="请输入"
                />
                <el-select v-else v-model="item.value" placeholder="请选择">
                  <el-option
                    v-for="option in item.options"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
      </el-card>

      <!-- 成本指标 -->
      <el-card shadow="hover" class="section-card card-left">
        <div slot="header">
          <h2>成本指标</h2>
        </div>
        <el-form :model="costForm" class="cost-form">
          <el-form-item label="完全成本">
            <el-input placeholder="请输入" v-model="costForm.fullCost">
              <el-select
                v-model="costForm.unit"
                slot="prepend"
                placeholder="单位"
              >
                <el-option label="￥" value="1" />
                <el-option label="$" value="2" />
              </el-select>
              <el-select
                v-model="costForm.unit"
                slot="append"
                placeholder="单位"
              >
                <el-option label="万元" value="1" />
                <el-option label="$" value="2" />
              </el-select>
            </el-input>
          </el-form-item>
          <el-form-item label="崖城13-1">
            <el-slider v-model="costForm.yaCheng13_1" show-input></el-slider>
          </el-form-item>
          <el-form-item label="崖城13-10"></el-form-item>
          <el-form-item label="崖城13-11"></el-form-item>
        </el-form>
      </el-card>

      <!-- 约束条件配置 -->
      <el-card shadow="hover" class="section-card">
        <div slot="header">
          <h2>指标预览</h2>
        </div>

        <div class="section-box">
          <div class="card-left">
            <h3>价格预览</h3>
            <el-table :data="supplyStrategy" border style="width: 100%">
              <el-table-column
                type="index"
                label="序号"
                width="100"
              ></el-table-column>
              <el-table-column
                prop="channel"
                label="油气类型"
                width="180"
              ></el-table-column>
              <el-table-column label="渠道">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.minSupply"
                    placeholder="请输入"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="价格">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.maxReceive"
                    placeholder="请输入"
                  ></el-input>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="card-right">
            <h3>销售预览</h3>
            <el-table :data="resultData" border style="width: 100%">
              <el-table-column
                v-for="item in columns"
                :key="item.channel"
                :prop="item.prop"
                :label="item.label"
              />
            </el-table>
          </div>
        </div>
      </el-card>

      <div class="action-buttons" v-if="!isCalculating">
        <el-button type="primary" size="large" @click="startCalculation"
          >开始测算</el-button
        >
      </div>

      <el-card shadow="hover" class="section-card" v-else>
        <div slot="header" class="card-header">
          <h2>测算结果</h2>
          <el-button type="primary" @click="resetCalculation">重置</el-button>
        </div>
        <div class="section-box">
          <div class="card-left">
            <div class="chart-box">
              <SalesVolume></SalesVolume>
            </div>
          </div>
          <div class="card-right">
            <el-table :data="fieldConstraints" border style="width: 100%">
              <el-table-column
                type="index"
                label="序号"
                width="100"
              ></el-table-column>
              <el-table-column
                label="价格指数变化量"
                prop="priceChange"
              ></el-table-column>
              <el-table-column
                label="指数变化率(%)"
                prop="priceChange"
              ></el-table-column>
              <el-table-column
                label="利润(万元)"
                prop="priceChange"
              ></el-table-column>
              <el-table-column
                label="利润影响(万元)"
                prop="priceChange"
              ></el-table-column>
            </el-table>
          </div>
        </div>
      </el-card>
    </el-card>
  </div>
</template>

<script>
import SalesVolume from "./salesVolume.vue";
export default {
  name: "priceSensitivity",
  components: {
    SalesVolume,
  },
  mounted() {
    window.document.documentElement.setAttribute("data-theme", "tint");
  },
  data() {
    return {
      modelType: "",
      isCalculating: false,
      formItems: [
        {
          label: "Brent价格基准：",
          prop: "bpBase",
          placeholder: "请输入",
          type: "input",
        },
        {
          label: "Brent取值下限：",
          prop: "dbpBase",
          placeholder: "请输入",
          type: "number",
        },
        {
          label: "Brent取值上限：",
          prop: "fbpBase",
          placeholder: "请输入",
          type: "input",
        },
        {
          label: "天然气计价单位：",
          prop: "unit",
          placeholder: "请输入",
          type: "input",
        },
        {
          label: "天然气销售单位：",
          prop: "sunit",
          placeholder: "请选择",
          type: "select",
          options: [
            { label: "吨", value: "1" },
            { label: "立方米", value: "2" },
          ],
        },
        {
          label: "凝析油计价单位：",
          prop: "ounit",
          placeholder: "请输入",
          type: "input",
        },
        {
          label: "凝析油销售单位：",
          prop: "osunit",
          placeholder: "请选择",
          type: "select",
          options: [
            { label: "吨", value: "1" },
            { label: "立方米", value: "2" },
          ],
        },
        {
          label: "计算汇率：",
          prop: "exchangeRate",
          placeholder: "请输入",
          type: "input",
        },
        {
          label: "JCC/Brent比率：",
          prop: "jccBrentRatio",
          placeholder: "请输入",
          type: "input",
        },
        {
          label: "指标单位：",
          prop: "indicatorUnit",
          placeholder: "请输入",
          type: "select",
          options: [
            { label: "吨", value: "1" },
            { label: "立方米", value: "2" },
          ],
        },
        {
          label: "最小变化量：",
          prop: "minChange",
          placeholder: "请输入",
          type: "input",
        },
      ],
      form: {
        bpBase: "",
        unit: "",
      },
      costForm: {},
      salesChannels: [
        { id: 1, channel: "香港中电", price: "", expansionCoefficient: "" },
        { id: 2, channel: "气电南山", price: "", expansionCoefficient: "" },
        { id: 3, channel: "气电广东", price: "", expansionCoefficient: "" },
      ],
      channels: ["崖城13-1", "崖城13-10", "陵水17-2", "陵水25-1"],
      selectedChannels: [],
      demandCenters: ["气电南山", "气电广东"],
      selectedDemandCenters: [],
      supplyStrategy: [
        { id: 1, channel: "香港中电", minSupply: "", maxReceive: "" },
        { id: 2, channel: "气电南山", minSupply: "", maxReceive: "" },
        { id: 3, channel: "气电广东", minSupply: "", maxReceive: "" },
      ],
      fieldConstraints: [
        {
          id: 1,
          priceChange: "唐城13-1",
          minProduction: "",
          maxProduction: "",
        },
        {
          id: 2,
          priceChange: "唐城13-10",
          minProduction: "",
          maxProduction: "",
        },
        {
          id: 3,
          priceChange: "陵水17-2",
          minProduction: "",
          maxProduction: "",
        },
        {
          id: 4,
          priceChange: "陵水25-1",
          minProduction: "",
          maxProduction: "",
        },
      ],
      columns: [
        { label: "油气类型", prop: "oilType" },
        { label: "渠道", prop: "channel" },
        { label: "崖城13-1", prop: "yancheng13_1" },
        { label: "崖城13-10", prop: "yancheng13_10" },
        { label: "陵水17-2", prop: "lingshui17_2" },
        { label: "陵水25-1", prop: "lingshui25_1" },
      ],
      resultData: [
        {
          oilType: "天然气",
          channel: "香港中电",
          yancheng13_1: "154",
          yancheng13_10: "256",
          lingshui17_2: "369",
          lingshui25_1: "485",
        },
        {
          oilType: "天然气",
          channel: "气电南山",
          yancheng13_1: "123",
          yancheng13_10: "456",
          lingshui17_2: "789",
          lingshui25_1: "321",
        },
        {
          oilType: "凝析油",
          channel: "气电广东",
          yancheng13_1: "654",
          yancheng13_10: "987",
          lingshui17_2: "321",
          lingshui25_1: "654",
        },
      ],
    };
  },
  methods: {
    startCalculation() {
      // 这里可以添加开始测算的逻辑
      this.$message.success("开始测算...");
      this.isCalculating = true;
    },
    resetCalculation() {
      this.isCalculating = false;
      // ToDo: 清空表单
    },
  },
};
</script>

<style lang="scss" scoped>
.price-sensitivity {
  padding: 20px;

  .box-card {
    margin-bottom: 20px;
  }

  .section-card {
    margin-bottom: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .section-box {
    display: flex;

    .card-left {
      flex: 1;
      margin-right: 12px;
    }

    .card-right {
      flex: 1;
    }
  }

  .action-buttons {
    text-align: center;
    margin-top: 30px;
    margin-bottom: 20px;
  }
}

::v-deep .el-card {
  border: 1px solid #edeef5;
}

::v-deep .el-input-group {
  width: 40%;
  .el-select {
    width: 80px;
  }
}

::v-deep .cost-form {
  .el-form-item {
    margin-bottom: 12px;
  }
}

::v-deep .el-slider {
  margin-left: 5%;
  width: 40%;
}

::v-deep .el-col {
  margin-bottom: 12px;
}
</style>
