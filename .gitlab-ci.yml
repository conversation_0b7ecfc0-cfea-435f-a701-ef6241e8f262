#代理地址： http://*************:8929/include/gitlab-ci.git

# 手动需要修改的变量，比如：PROJECT_CODE不为群组名，手动修改。如为默认群组名，可省略
variables:
  # 默认variables：http://*************:8929/include/gitlab-ci/-/blob/master/variables.yml
  PROJECT_CODE: shyh-hw
  PROJECT_CODE_PRD: shyh-hw
  #charts目录的路径
  CHARTS_PATHS: ./charts
  DOCKERFILE_PATH: ./docker
  #NODE_IMAGES: harbor.cfbgcfront.com/cpcc/node:14.18.0
  PROJECT_HUAWEI_ID: 9c5a9fb073dd4358b4e66bf23f96c644

############------------------默认---------------------##################
#默认stages，如无特殊需求，不需要修改
include:
  - project: 'include/gitlab-ci'
    ref: master
    file: 'stages.yml'

#默认PROJECT_CODE为群组名，如群组名与k8s命名空间不一至，在上面手动修改、覆盖
  - project: 'include/gitlab-ci'
    ref: master
    file: 'variables.yml'

############------------------npm发版至开发环境----------------------##################

# npm后端发版至dev环境--master--package
  - project: 'include/gitlab-ci'
    ref: master
    file: 'front/npm-build-job/npm-job.yml'

# npm后端发版至dev环境--master--docker-build-job
  - project: 'include/gitlab-ci'
    ref: master
    #file: 'docker_build_job_dev.yml'
    file: 'front/docker-build-job/docker-build-front-master-job.yml'

# npm后端发版至dev环境--master--deploy_k8s
  - project: 'include/gitlab-ci'
    ref: master
    file: 'front/deploy-k8s-job/deploy-k8s-cpoc-front-job.yml'
    

############-------------------npm发版至华为云生产环境-------------------------##################

# npm后端发版至release环境--release--docker-build-job
  - project: 'include/gitlab-ci'
    ref: master
    file: 'front/docker-build-job/docker-push-front-code-huawei-cloud-job.yml'


npm_build_job:
  script:
    # 替换生产环境信息
    # - npm config set proxy null
    - npm cache clean --force
    - echo $NPM_236_LOGIN > ~/.npmrc
    - npm config set registry http://*************:8081/repository/npm-group
    # - rm -rf package-lock.json
    # - npm config set registry http://registry.npmjs.org/
    - rm -rf node_modules
    - npm install --force
    - npm run build && pwd
