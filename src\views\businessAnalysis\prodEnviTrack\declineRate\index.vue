<template>
  <div class="declineRate">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  name: "declineRate",
  data() {
    return {
      xData: ["2021年", "2022年", "2023年", "2024年", "2025年"],
      y1Data: [6.3, 4.5, 4, 7, 8.4],
      y2Data: [4.5, 3.6, 3, 6, 7.6],
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  methods: {
    initChart() {
      let mychart = echarts.init(this.$refs.chartBox);
      this.option = {
        color: ["#248EFF","#7262FD"], //圆柱体颜色
        tooltip: {
          trigger: "item",
          padding: 1,
          formatter: function (param) {},
        },
        legend: {
          data: ["自然递减率", "综合递减率"],
          textStyle: {
            color: "#ffffff",
            fontSize: 12,
          },
          icon: "rect",
          itemWidth: 8,
          itemHeight: 8,
          right: "10%",
          top: "2%",
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: this.xData,
            axisTick: {
              alignWithLabel: true,
            },
            nameTextStyle: {
              color: "#ACC2E2", //文本颜色
            },
            axisLine: {
              lineStyle: {
                color: "rgba(172, 194, 226, 0.2)", //轴线颜色
              },
            },
            axisLabel: {
              textStyle: {
                color: "#ACC2E2", //轴线文本颜色
              },
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "%",
            position: "left",
            nameTextStyle: {
              color: "#ACC2E2",
              align: "right",
            },
            axisLabel: {
              textStyle: {
                color: "#ACC2E2",
              },
              formatter: "{value}",
            },
            splitLine: {
              lineStyle: {
                color: "rgba(172, 194, 226, 0.2)",
              },
            },
          }
        ],
        series: [
          {
            name: "自然递减率",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [-12, -5],
            symbolPosition: "end",
            z: 12,
            label: {
              normal: {
                show: false,
              },
            },
            data: this.y1Data,
            markLine: {
              symbol: "none",
              label:{
                position:'start',
                formatter: '{b}',
              },
              lineStyle: {
                color: "#FF6660",
                type: "line",
              },
              data:[{
                yAxis: 3.6,
              }]
            }
          },
          {
            name: "自然递减率",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [-12, 5],
            z: 12,
            data: this.y1Data,
          },
          {
            name: "自然递减率",
            type: "bar",
            itemStyle: {
              normal: {
                opacity: 0.7,
              },
            },
            barWidth: "20",
            data: this.y1Data,
          },

           {
            name: "综合递减率",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [12, -5],
            symbolPosition: "end",
            z: 12,
            label: {
              normal: {
                show: false,
              },
            },
            data: this.y2Data,
          },
          {
            name: "综合递减率",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [12, 5],
            z: 12,
            data: this.y2Data,
          },
          {
            name: "综合递减率",
            type: "bar",
            itemStyle: {
              normal: {
                opacity: 0.7,
              },
            },
            barWidth: "20",
            data: this.y2Data,
          },
          
        ],
      };
      mychart.setOption(this.option);
    },
  },
};
</script>
<style>
.declineRate {
  width: 100%;
  .chart-box {
    width: 95%;
    height: 300px;
  }
}
</style>
