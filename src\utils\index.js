/*
 * @description 获取不同格式的时间
 * @param (myDate) new Date(日期) 不传默认今天
 *
 * 使用：
 * today().Y 结果：2020
 * today().YM 结果：2020-12
 * ......
 * 详见return
 *
 * */


export function getToday(myDate) {
    let date
    if (myDate) {
        date = new Date(myDate)
        if (isNumber(myDate)) {
            let dayDiffer = ''
            if (myDate == -30) {
                dayDiffer = new Date().setMonth(new Date().getMonth() - 1)
            } else if (myDate == 0) {
                dayDiffer = new Date(getToday().YMD)
            } else {
                let timeNum = new Date().getTime()
                dayDiffer = myDate * 24 * 60 * 60 * 1000 + timeNum
            }
            date = new Date(dayDiffer)
        }
    } else {
        date = new Date()
    }
    let Y = date.getFullYear()
    let W = getWeek(date)
    let M = date.getMonth() + 1
    let M2 = date.getMonth()
    M = M < 10 ? '0' + M : M
    M2 = M2 < 10 ? '0' + M2 : M2
    let D = date.getDate()
    let D2 = date.getDate() - 1
    D = D < 10 ? '0' + D : D
    D2 = D2 < 10 ? '0' + D2 : D2
    let H = date.getHours()
    H = H < 10 ? '0' + H : H
    let Mi = date.getMinutes()
    Mi = Mi < 10 ? '0' + Mi : Mi
    let S = date.getSeconds()
    S = S < 10 ? '0' + S : S

    return {
        Y: Y + '', // 年
        YM: Y + '-' + M, // 年-月
        YM2: Y + '-' + M2, // 年-月
        YM3:Y + '年' + M2 + '月',
        YMD: Y + '-' + M + '-' + D, // 年-月-日
        YMD2: Y + '-' + M + '-' + D2, // 年-月-日
        W: Y + '-' + `${W}`, // 年-周
        YMDHM: Y + '-' + M + '-' + D + ' ' + H + ':' + Mi, // 年-月-日 时-分
        YMDHMS: Y + '-' + M + '-' + D + ' ' + H + ':' + Mi + ':' + S // 年-月-日 时-分-秒
    }
}

/**
 * 校验是数字就返回true
 **/

function isNumber(val) {
    var regPos = /^\d+(\.\d+)?$/ //非负浮点数
    var regNeg = /^(-(([0-9]+\.[0-9]*[1-9][0-9]*)|([0-9]*[1-9][0-9]*\.[0-9]+)|([0-9]*[1-9][0-9]*)))$/ //负浮点数
    if (regPos.test(val) || regNeg.test(val)) {
        return true
    } else {
        return false
    }
}

function getWeek(dateTime) {
    let temptTime = new Date(dateTime);
    //周几
    let weekday = temptTime.getDay() || 7;
    //周1+5天=周六
    temptTime.setDate(temptTime.getDate() - weekday + 1 + 5);
    let firstDay = new Date(temptTime.getFullYear(), 0, 1);
    let dayOfWeek = firstDay.getDay();
    let spendDay = 1;
    if (dayOfWeek != 0) {
        spendDay = 7 - dayOfWeek + 1;
    }
    firstDay = new Date(temptTime.getFullYear(), 0, 1 + spendDay);
    let d = Math.ceil((temptTime.valueOf() - firstDay.valueOf()) / 86400000);
    let result = Math.ceil(d / 7) + 1;

    return result < 10 ? `0${result}` : result;
}

/*单个文件*/
export function exportFile(fileResult, fileName = '文件.xls') {
    const blob = new Blob([fileResult], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    if ('download' in document.createElement('a')) {
        // 非IE下载
        const elink = document.createElement('a')
        elink.download = fileName
        elink.style.display = 'none'
        elink.href = URL.createObjectURL(blob)
        document.body.appendChild(elink)
        elink.click()
        URL.revokeObjectURL(elink.href) // 释放URL 对象
        document.body.removeChild(elink)
        console.log('elink',elink)
    } else {
        // IE10+下载
        navigator.msSaveBlob(blob, fileName)
    }
}

// 压缩包
export function exportFile_zip(fileResult, fileName) {
    const blob = new Blob([fileResult], {
        // 此处文件流类型应与后端保持一致，后端反的是zip的，故我也是zip
        type: 'application/zip'
    })
    if ('download' in document.createElement('a')) {
        // 非IE下载
        const elink = document.createElement('a')
        console.log(elink)
        elink.download = fileName
        elink.style.display = 'none'
        elink.href = URL.createObjectURL(blob)
        document.body.appendChild(elink)
        elink.click()
        URL.revokeObjectURL(elink.href) // 释放URL 对象
        document.body.removeChild(elink)
    } else {
        // IE10+下载
        navigator.msSaveBlob(blob, fileName)
    }
}

//  截取文件后缀名
export function getFileType(fname) {
    var fileName = fname.lastIndexOf("."); //取到文件名开始到最后一个点的长度
    var fileNameLength = fname.length; //取到文件名长度
    var fileFormat = fname.substring(fileName + 1, fileNameLength); //截
    return fileFormat;
}


export function getFileNameBypath(fullpath) {
    let obj = fullpath.lastIndexOf("/");
    return [fullpath.substr(obj + 1), fullpath.substr(0, obj)]
}

// 附上工具
function isJSON(target) {
    return typeof target == "object" && target.constructor == Object;
}

export function getDiffDay(date_1, date_2) {
    // 计算两个日期之间的差值
    let totalDays, diffDate
    let myDate_1 = Date.parse(date_1)
    let myDate_2 = Date.parse(date_2)
    // 将两个日期都转换为毫秒格式，然后做差
    diffDate = myDate_1 - myDate_2 // 取相差毫秒数的绝对值
    totalDays = Math.floor(diffDate / (1000 * 3600 * 24)) // 向下取整
    return totalDays    // 相差的天数
}


export function getDiffHour(start, end) {
    if (!end) {
        return ''
    }
    let end_ = JSON.parse(JSON.stringify(end))
    if (end_ == '00:00') {
        end_ = '24:00'
    }
    start = '2009-11-5 ' + start
    end_ = '2009-11-5 ' + end_
    if (typeof (start) == "string") {
        start = new Date(start.replace(/-/, '/'));
        end_ = new Date(end_.replace(/-/, '/'));
    }
    var res = end_ - start;
    if (isNaN(res)) {
        return ''
    }

    return (res / (1000 * 60 * 60)).toFixed(2);
}

export function getAnalysisIdCard(card, num) {
    if (num == 1) {
        //获取出生日期
        let birth = card.substring(6, 10) + "-" + card.substring(10, 12) + "-" + card.substring(12, 14);
        return birth;
    }


    if (num == 2) {
        var myDate = new Date();

        var month = myDate.getMonth() + 1;

        var day = myDate.getDate();

        var age = myDate.getFullYear() - card.substring(6, 10) - 1;

        if (card.substring(10, 12) < month || card.substring(10, 12) == month && card.substring(12, 14) <= day)
            age++;
        return age;
    }
}

export function getoprAreaList_item(list, arr = []) {
    let newArr = arr
    if (!list) {
        return
    }
    list.forEach((item) => {
        if (item.children && item.children.length == 0 || !item.children) {
            newArr.push({
                personTypeName: item.dictLabel,
                personNum: '',
                personTypeValue: item.dictValue,
                personneCategory: findParentIds(store.getters._dr_person_type_list, item.dictValue)[findParentIds(store.getters._dr_person_type_list, item.dictValue).length - 1]
            })
        }
        if (item.children && item.children.length > 0) {
            getoprAreaList_item(item.children, newArr)
        }
    })
    return [...new Set(newArr)]
}

// 传入参数：需要遍历的arr，需要匹配的末级id
export function findParentIds(dataSource, nodeId) {
    const parentIds = []; // 用于存储所有父节点ID的数组
    // 定义一个递归函数，用于遍历整棵树并查找子节点的所有父节点
    function traverse(node, nodeId) {
        if (node.dictValue === nodeId) {
            parentIds.push(node.dictLabel);// 如果当前节点的ID等于子节点的ID，则表示已经找到了子节点，可以开始向上查找父节点
            return true; // 返回true表示已经找到了子节点
        }
        if (node.children) { // 如果当前节点有子节点，则继续遍历子节点
            for (const childNode of node.children) {
                if (traverse(childNode, nodeId)) { // 如果在子节点中找到了子节点的父节点，则将当前节点的ID添加到父节点ID数组中，并返回true表示已经找到了子节点
                    parentIds.push(node.dictLabel);
                    return true;
                }
            }
        }

        return false; // 如果当前节点不是子节点的父节点，则返回false
    }

    // 从根节点开始遍历整棵树，并调用递归函数查找子节点的所有父节点
    for (const node of dataSource) {
        if (traverse(node, nodeId)) { // 如果在当前节点的子树中找到了子节点的父节点，则直接退出循环
            break;
        }
    }

    return parentIds; // 返回所有父节点ID的数组
}


// 传入参数：需要遍历的arr，需要匹配的末级id 可以自定义字段
export function findParentIds_new(dataSource, nodeId, key1, key2) {
    const parentIds = []; // 用于存储所有父节点ID的数组
    // 定义一个递归函数，用于遍历整棵树并查找子节点的所有父节点
    function traverse(node, nodeId, key1, key2) {
        if (node[key1] == nodeId) {
            parentIds.unshift(node[key2]);// 如果当前节点的ID等于子节点的ID，则表示已经找到了子节点，可以开始向上查找父节点
            return true; // 返回true表示已经找到了子节点
        }
        if (node.children) { // 如果当前节点有子节点，则继续遍历子节点
            for (const childNode of node.children) {
                if (traverse(childNode, nodeId, key1, key2)) { // 如果在子节点中找到了子节点的父节点，则将当前节点的ID添加到父节点ID数组中，并返回true表示已经找到了子节点
                    parentIds.unshift(node[key2]);
                    return true;
                }
            }
        }

        return false; // 如果当前节点不是子节点的父节点，则返回false
    }

    // 从根节点开始遍历整棵树，并调用递归函数查找子节点的所有父节点
    for (const node of dataSource) {
        if (traverse(node, nodeId, key1, key2)) { // 如果在当前节点的子树中找到了子节点的父节点，则直接退出循环
            break;
        }
    }

    return parentIds; // 返回所有父节点ID的数组
}

export function getLabel(val, item_c, list, str) {
    list.forEach((item) => {
        if (item.dictValue === val) {
            item_c[str] = item.dictLabel
        }
    })
}

export function getOpt_name(val, list, label, value) {
    let label_val = ''
    if (list.length == 0) {
        return
    } else {
        list.forEach((item) => {
            if (item[value] === val) {
                label_val = item[label]
            }
        })
    }
    return label_val == '全部' ? '--' : label_val
}

export function deepMearge(obj, target = {}) {
    // target 替换 obj
    let p = {}; // 是为了调用 Object.prototype.toSring方便
    for (let key in target) {
        let isA = p.toString.call(obj[key]) === '[object Object]',
            isB = p.toString.call(target[key]) === '[object Object]';
        if (isA && isB) {
            obj[key] = deepMearge(obj[key], target[key])
        } else if (Array.isArray(obj[key]) && Array.isArray(target[key])) {
            // Array.from 方法可以把一个类数组对象转为数组
            obj[key] = Array.from(new Set(obj[key].concat(target[key]))
            )
        } else {
            obj[key] = target[key];
        }
    }
    return obj;
}

/**
 * 通过出生日期获取当前年龄
 * @param strBirthday：指的是出生日期，格式为"1990-01-01",字符串类型
 */
export function getCurrentAgeByBirthDate(strBirthday) {
    // 将出生日期的字符串通过"-"分割成数组
    const strBirthdayArr = strBirthday.split("-")
    // 拿到出生日期的年
    const birthYear = strBirthdayArr[0]
    // 拿到出生日期的月
    const birthMonth = strBirthdayArr[1]
    // 拿到出生日期的日
    const birthDay = strBirthdayArr[2]
    // 创建一个时间对象
    const d = new Date()
    // 拿到当前时间的年
    const nowYear = d.getFullYear()
    // 拿到当前时间的月
    const nowMonth = d.getMonth() + 1
    // 拿到当前时间的日
    const nowDay = d.getDate()
    // 如果出生日期的年等于当前时间的年
    if (nowYear === birthYear) return 0 // 返回周岁年龄 0,并终止函数执行
    // 如果如果出生日期的年不等于于当前时间的年,则拿到年之差
    const ageDiff = nowYear - birthYear; // 年之差
    // 如果年之差是个负数,则表示用户输入的出生日期错误,晚于今天,返回 -1,并终止函数执行
    if (ageDiff < 0) return -1 // 返回错误 -1,并终止函数执行
    // 如果年之差是个正整数,但出生日期的月与当前时间的月不相等
    if (nowMonth !== birthMonth) {
        // 拿到出生日期的日与当前时间的月之差
        const monthDiff = nowMonth - birthMonth; // 月之差
        // 如果月之差是个负数,则年之差 - 1后得到周岁年龄,否则直接得到周岁年龄
        return monthDiff < 0 ? ageDiff - 1 : ageDiff  // 返回周岁年龄,并终止函数执行
    }
    // 如果出生日期的月与当前时间的月相等,则拿到出生日期的日与当前时间的日之差
    const dayDiff = nowDay - birthDay;
    // 如果日之差是个负数,则年之差 - 1得到周岁年龄,否则直接得到周岁年龄
    return dayDiff < 0 ? ageDiff - 1 : ageDiff // 返回周岁年龄,并终止函数执行
}

/*
 * @name: dateDiff
 * @description: 计算两个日期相差天数
 * @param：eDate {String} 结束时间
               sDate {String} 开始时间
 *         splitStr {String} 默认值'-'，如（2010-10-10的分隔符为“-”）这里是为了通用各种分隔符的日期格式
 * @return： {Number} 天数
 * @example： dateDiff('2020-08-03', '2020-08-01', '-')  =>  2
 */
export const dateDiff = (date_1, date_2) => {
    let totalDays, diffDate
    let myDate_1 = Date.parse(date_1)
    let myDate_2 = Date.parse(date_2)
    // 将两个日期都转换为毫秒格式，然后做差
    diffDate = Math.abs(myDate_1 - myDate_2) // 取相差毫秒数的绝对值
    totalDays = (diffDate / (1000 * 3600 * 24)) // 向下取整
    return totalDays    // 相差的天数
}

export function getLabelName(id, list, form, key) {
    console.log(id, list, form, key)
    list.forEach((item) => {
        if (item.value == id || item.deptId == id) {
            console.log('@@', id, item)
            this.$set(form, key, item.label || item.deptName)
        }
    })
}

export function getFileSize(kb) {
    if (isNaN(kb)) {
        return 0 + 'MB'
    }
    let m = (kb / 1024 / 1024).toFixed(3)
    return m + 'MB'
}


const validateIdent = {
    aIdentityCode_City: { // 城市代码列表
        11: "北京", 12: "天津", 13: "河北", 14: "山西", 15: "内蒙古", 21: "辽宁", 22: "吉林",
        23: "黑龙江 ", 31: "上海", 32: "江苏", 33: "浙江", 34: "安徽", 35: "福建", 36: "江西",
        37: "山东", 41: "河南", 42: "湖北 ", 43: "湖南", 44: "广东", 45: "广西", 46: "海南",
        50: "重庆", 51: "四川", 52: "贵州", 53: "云南", 54: "西藏 ", 61: "陕西", 62: "甘肃",
        63: "青海", 64: "宁夏", 65: "新疆", 71: "台湾", 81: "香港", 82: "澳门", 91: "国外 "
    },
    IdentityCode_isCardNo(card) {//检查号码是否符合规范，包括长度，类型
        var reg = /(^\d{15}$)|(^\d{17}(\d|X)$)/; //身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X
        if (reg.test(card) === false) {
            return false;
        }
        return true;
    },
    IdentityCode_checkProvince(card) { //取身份证前两位，校验省份
        var province = card.substr(0, 2);
        if (validateIdent.aIdentityCode_City[province] == undefined) {
            return false;
        }
        return true;
    },
    IdentityCode_checkBirthday(card) { //检查生日是否正确，15位以'19'年份来进行补齐。
        var len = card.length;
        //身份证15位时，次序为省（3位）市（3位）年（2位）月（2位）日（2位）校验位（3位），皆为数字
        if (len == '15') {
            var re_fifteen = /^(\d{6})(\d{2})(\d{2})(\d{2})(\d{3})$/;
            var arr_data = card.match(re_fifteen); // 正则取号码内所含出年月日数据
            var year = arr_data[2];
            var month = arr_data[3];
            var day = arr_data[4];
            var birthday = new Date('19' + year + '/' + month + '/' + day);
            return validateIdent.IdentityCode_verifyBirthday('19' + year, month, day, birthday);
        }
        //身份证18位时，次序为省（3位）市（3位）年（4位）月（2位）日（2位）校验位（4位），校验位末尾可能为X
        if (len == '18') {
            var re_eighteen = /^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X)$/;
            var arr_data = card.match(re_eighteen); // 正则取号码内所含出年月日数据
            var year = arr_data[2];
            var month = arr_data[3];
            var day = arr_data[4];
            var birthday = new Date(year + '/' + month + '/' + day);
            return validateIdent.IdentityCode_verifyBirthday(year, month, day, birthday);
        }
        return false;
    },
    IdentityCode_verifyBirthday(year, month, day, birthday) {//校验日期 ，15位以'19'年份来进行补齐。
        var now = new Date();
        var now_year = now.getFullYear();
        //年月日是否合理
        if (birthday.getFullYear() == year
            && (birthday.getMonth() + 1) == month
            && birthday.getDate() == day) {
            //判断年份的范围（3岁到150岁之间)
            var time = now_year - year;
            if (time >= 3 && time <= 150) {
                return true;
            }
            return false;
        }
        return false;
    },
    IdentityCode_checkParity(card) { //校验位的检测
        card = validateIdent.IdentityCode_changeFivteenToEighteen(card); // 15位转18位
        var len = card.length;
        if (len == '18') {
            var arrInt = new Array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2);
            var arrCh = new Array('1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2');
            var cardTemp = 0, i, valnum;
            for (i = 0; i < 17; i++) {
                cardTemp += card.substr(i, 1) * arrInt[i];
            }
            valnum = arrCh[cardTemp % 11];
            if (valnum == card.substr(17, 1)) {
                return true;
            }
            return false;
        }
        return false;
    },
    IdentityCode_changeFivteenToEighteen(card) {  //15位转18位身份证号
        if (card.length == '15') {
            var arrInt = new Array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2);
            var arrCh = new Array('1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2');
            var cardTemp = 0, i;
            card = card.substr(0, 6) + '19' + card.substr(6, card.length - 6);
            for (i = 0; i < 17; i++) {
                cardTemp += card.substr(i, 1) * arrInt[i];
            }
            card += arrCh[cardTemp % 11];
            return card;
        }
        return card;
    },
    IdentityCodeValid(card) {//   身份证号码检验主入口
        let pass = true;
        let sex = ''
        //是否为空
        if (pass && card === '')
            pass = false;
        //校验长度，类型
        if (pass && validateIdent.IdentityCode_isCardNo(card) === false)
            pass = false;
        //检查省份
        if (pass && validateIdent.IdentityCode_checkProvince(card) === false)
            pass = false;
        //校验生日
        if (pass && validateIdent.IdentityCode_checkBirthday(card) === false)
            pass = false;
        //检验位的检测
        if (pass && validateIdent.IdentityCode_checkParity(card) === false)
            pass = false;
        if (pass) {
            var iCard = validateIdent.IdentityCode_changeFivteenToEighteen(card);
            if (parseInt(iCard.charAt(16)) % 2 == 0) {
                sex = "0"; // 女生
            } else {
                sex = "1"; // 男生
            }
            return true
        } else {
            return false
        }
    }
}


export default validateIdent   //导出

export function handleCascader(list) {
    if (!list.length || list.length == 0) {
        return
    }
    list.forEach(item => {
        if (item.children && item.children.length > 0) {
            handleCascader(item.children)
        } else {
            item.children = undefined
        }
    })
    return list
}

export function generateUUID() {
    var d = new Date().getTime();
    if (typeof performance !== 'undefined' && typeof performance.now === 'function') {
        d += performance.now(); // 使用性能相关数据提高唯一性
    }
    var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = (d + Math.random() * 16) % 16 | 0;
        d = Math.floor(d / 16);
        return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);
    });
    return uuid;
}

//委托单-所需父委托单/单据来源(审核完成和不需要审核的)
export async function getAllProlist(orderType = '') {
    const res = await get_operateEntrustOrders({
        orderType
    })
    if (res.code == 200) {
        return res.data
    } else return []
}


export async function queryEntrustsAll_util(orderType, status) {
    const res = await queryEntrustsAll({
        orderType, status
    })
    if (res.code == 200) {
        return res.data
    } else return []
}


export async function get_dictionary_data(dictType) {
    const res = await getOptList({
        dictType
    })
    if (res.code == 200) {
        return res.data
    } else return []
}


export async function get_listByRole_person(params) {
    const res = await listByRole_person(params)
    if (res.code == 200) {
        return res.data
    } else return []
}

export async function diguiRole_list(list) {
    list.forEach((item) => {
        if (item.children && item.children.length > 0) {
            diguiRole_list(item.children)
        } else {
            if (item.type == '2' || item.type == '3') {
                item.disabled = true
            }
        }
    })
}


export function GetAge(identityCard) {
    var len = (identityCard + "").length;
    if (len == 0) {
        return 0;
    } else {
        if ((len != 15) && (len != 18))//身份证号码只能为15位或18位其它不合法
        {
            return 0;
        }
    }
    var strBirthday = "";
    if (len == 18)//处理18位的身份证号码从号码中得到生日和性别代码
    {
        strBirthday = identityCard.substr(6, 4) + "/" + identityCard.substr(10, 2) + "/" + identityCard.substr(12, 2);
    }
    if (len == 15) {
        strBirthday = "19" + identityCard.substr(6, 2) + "/" + identityCard.substr(8, 2) + "/" + identityCard.substr(10, 2);
    }
    //时间字符串里，必须是“/”
    var birthDate = new Date(strBirthday);
    var nowDateTime = new Date();
    var age = nowDateTime.getFullYear() - birthDate.getFullYear();
    //再考虑月、天的因素;.getMonth()获取的是从0开始的，这里进行比较，不需要加1
    if (nowDateTime.getMonth() < birthDate.getMonth() || (nowDateTime.getMonth() == birthDate.getMonth() && nowDateTime.getDate() < birthDate.getDate())) {
        age--;
    }
    return age;
}


export function treeFindPath(tree, func, path = []) {
    if (!tree) return []
    for (const data of tree) {
        path.push(data.id)
        if (func(data)) return path
        if (data.children) {
            const findChildren = treeFindPath(data.children, func, path)
            if (findChildren.length) return findChildren
        }
        path.pop()
    }
    return []
}

export function downLoadBy_common(filePath, name) {
    console.log(filePath)
    const query = {filePath}
    file_download(query).then((res) => {
        exportFile(res, name)
    })
}

export function deepCopy(data, hash = new WeakMap()) {
    if (typeof data !== 'object' || data === null) {
        throw new TypeError('传入参数不是对象')
    }
    // 判断传入的待拷贝对象的引用是否存在于hash中
    if (hash.has(data)) {
        return hash.get(data)
    }
    let newData = {};
    const dataKeys = Object.keys(data);
    dataKeys.forEach(value => {
        const currentDataValue = data[value];
        // 基本数据类型的值和函数直接赋值拷贝
        if (typeof currentDataValue !== "object" || currentDataValue === null) {
            newData[value] = currentDataValue;
        } else if (Array.isArray(currentDataValue)) {
            // 实现数组的深拷贝
            newData[value] = [...currentDataValue];
        } else if (currentDataValue instanceof Set) {
            // 实现set数据的深拷贝
            newData[value] = new Set([...currentDataValue]);
        } else if (currentDataValue instanceof Map) {
            // 实现map数据的深拷贝
            newData[value] = new Map([...currentDataValue]);
        } else {
            // 将这个待拷贝对象的引用存于hash中
            hash.set(data, data)
            // 普通对象则递归赋值
            newData[value] = deepCopy(currentDataValue, hash);
        }
    });
    return newData;
}

// 自适应高度
export function getScreenRateValue(num = 1) {
    let clientWidth =
        window.innerWidth ||
        document.documentElement.clientWidth ||
        document.body.clientWidth
    let sR = clientWidth ? clientWidth / 1920 : 1
    return sR * num
}

export function getFileIds(list) {
    if (!list) {
        return
    }
    let arr = []
    list.forEach((item) => {
        arr.push(item.fileId || item.id)
    })
    return arr
}

/* 字段为空 默认值显示为 --*/
export function showVal(val) {
    return (val === undefined || val === null || val === '') ? '--' : val;
}


export function flattenArrayWithChildren(arr) {
    let result = [];
    arr.forEach(item => {
        result.push(item);
        if (item.children && item.children.length > 0) {
            const flattenedChildren = flattenArrayWithChildren(item.children);
            result = result.concat(flattenedChildren);
        }
    });

    return result;
}


export function transformArray(data) {
    const result = [];
    const contentType1 = data.filter(item => item.contentType === 1);
    const contentType2 = data.filter(item => item.contentType === 2);
    contentType1.forEach(item => {
        const newItem = item
        contentType2.forEach(childItem => {
            if (childItem.packageCode === item.packageCode) {
                if (!newItem.children) {
                    newItem.children = []
                }
                newItem.children.push(childItem);
            }
        });

        result.push(newItem);
    });
    if (result.length > 0) {
        return result
    } else {
        return data;
    }

}

export function compareArrays(array1, array2) {
    return array1.filter(item => !array2.includes(item)).concat(array2.filter(item => !array1.includes(item)));
}


/*根据id 遍历名字*/
export function getNameByid(tree, id) {
    if (!id) {
        return ''
    }
    for (let node of tree) {
        if (node.id === id) {
            return node.label;
        }
        if (node.children) {
            const foundName = getNameByid(node.children, id);
            if (foundName) {
                return foundName;
            }
        }
    }
    return null;
}


/*根据id 遍历名字*/
export function getNameByKey(tree, val, key, label = 'label') {
    if (!val) {
        return ''
    }
    for (let node of tree) {
        if (node[key] === val) {
            return node[label];
        }
        if (node.children) {
            const foundName = getNameByid(node.children, val);
            if (foundName) {
                return foundName;
            }
        }
    }
    return null;
}

export function formatDateString(dateString, format) {
    const date = new Date(dateString);

    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();

    let formattedDate = format;
    formattedDate = formattedDate.replace('yyyy', year);
    formattedDate = formattedDate.replace('MM', month < 10 ? '0' + month : month);
    formattedDate = formattedDate.replace('dd', day < 10 ? '0' + day : day);

    return formattedDate;
}

// 防抖
export function _debounce(fn, delay) {
    const delays = delay || 200;
    let timer;
    return function () {
        const th = this;
        const args = arguments;
        if (timer) {
            clearTimeout(timer);
        }
        timer = setTimeout(() => {
            timer = null;
            fn.apply(th, args);
        }, delays);
    };
}

export function beforeFileUpload(file) {
    const isLt50M = file.size / 1024 / 1024 < 50;
    if (!isLt50M) {
        this.$message.error("文件不能超过50MB!");
    }
    return isLt50M;
}

export function getFileNameBypath_(fullpath) {
    let obj = fullpath.lastIndexOf("/");
    return fullpath.substr(obj + 1)
}
