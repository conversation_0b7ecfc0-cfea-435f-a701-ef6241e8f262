<template>
  <div class="gasProduction">
    <div class="line-chart" ref="lineChart"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  name: "gasProduction",
  data() {
    return {};
  },
  mounted() {
    this.initChart();
  },
  methods: {
    initChart() {
      let chart = echarts.init(this.$refs.lineChart);
      let option = {
        legend: {
          data: ["天然气"],
          textStyle: {
            color: "#ffffff",
            fontSize: 12,
          },
          icon: "rect",
          itemWidth: 8,
          itemHeight: 8,
          right: "10%",
          top: "2%",
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: ["2021年", "2022年", "2023年", "2024年", "2025年"],
          axisTick: {
            show: false, // 不显示刻度线
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            inside: false,
            textStyle: {
              color: "#ACC2E2", // x轴颜色
              fontWeight: "normal",
              fontSize: "14",
              lineHeight: 22,
            },
          },
        },
        yAxis: {
          type: "value",
          splitLine: {
            lineStyle: {
              color: "rgba(172, 194, 226, 0.2)", // 设置y轴网格线颜色
            },
          },
          axisLabel: {
            inside: false,
            textStyle: {
              color: "#ACC2E2", // x轴颜色
              fontWeight: "normal",
              fontSize: "14",
              lineHeight: 22,
            },
          },
        },
        series: [
          {
            name: "天然气",
            data: [200, 380, 120, 80, 210],
            type: "line",
            itemStyle: {
              color: "#FF6660", // 设置线条颜色为红色
            },
            symbol: "circle",
            symbolSize: 10, // 图标大小
          },
        ],
      };
      chart.setOption(option);
    },
  },
};
</script>
<style lang="scss" scoped>
.gasProduction {
  width: 100%;
  .line-chart {
    width: 100%;
    height: 238px;
  }
}
</style>
