var img = [
  "@/assets/tableicon/line1.png",
  "@/assets/tableicon/line2.png",
  "@/assets/tableicon/line3.png",
];
var color = ["#00f8ff", "#00f15a", "#0696f9", "#dcf776"];

export function getOption(xData, yData, Line) {
  // console.log("获取的数据", yData);

  var datas = [];
  Line.map((item, index) => {
    datas.push({
      name: item,
      type: "line",
      yAxisIndex: 1,
      data: yData[index],
      itemStyle: {
        normal: {
          borderWidth: 5,
          color: color[index],
        },
      },
      markLine: {
        symbol: "none",
        lineStyle: {
          color: color[index],
          type:"solid",
        },
        data:[
          {
            name:"平均值",
            yAxis: yData[index].reduce((a, b) => a + b, 0) / yData[index].length, // 计算平均值
          }
        ]
      }
    });
  });

  let option = {
    backgroundColor: "#0e2147",
    grid: {
      left: "36px",
      bottom: "32px",
    },
    legend: {
      type: "scroll",
      data: Line,
      itemWidth: 18,
      itemHeight: 12,
      textStyle: {
        color: "#00ffff",
        fontSize: 14,
      },
    },
    yAxis: [
      {
        type: "value",
        position: "left",
        nameTextStyle: {
          color: "#fff",
        },
        splitLine: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
      },
      {
        type: "value",
        position: "left",
        nameTextStyle: {
          color: "#00FFFF",
        },
        splitLine: {
          lineStyle: {
            type: "dashed",
            color: "rgba(135,140,147,0.8)",
          },
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          formatter: "{value}",
          color: "#fff",
          fontSize: 14,
        },
      },
    ],
    xAxis: [
      {
        type: "category",
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: "#6A989E",
          },
        },
        axisLabel: {
          inside: false,
          textStyle: {
            color: "#fff", // x轴颜色
            fontWeight: "normal",
            fontSize: "14",
            lineHeight: 22,
          },
        },
        data: xData,
      },
    ],
    series: datas,
  };
  return option;
}
