<template>
  <div class="complete-chart">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  name: "completeChart",
  data() {
    return {
      mychart: null,
      // 示例数据结构：每个项目包含实际值和预期值
      chartData: [
        { name: "陵水17-2", actual: 10, expected: 15 },
        { name: "崖城13-1", actual: 45, expected: 55 },
        { name: "陵水25-1", actual: 23, expected: 35 },
        { name: "文昌16-2", actual: 88, expected: 95 },
        { name: "崖城13-10", actual: 62, expected: 75 }
      ]
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
    // 监听窗口大小变化，自动调整图表
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
    if (this.mychart) {
      this.mychart.dispose();
    }
  },
  methods: {
    initChart() {
      this.mychart = echarts.init(this.$refs.chartBox);

      // 提取数据
      const categories = this.chartData.map(item => item.name);
      const expectedData = this.chartData.map(item => item.expected);
      const actualData = this.chartData.map(item => item.actual);



      const option = {

        tooltip: {
          trigger: "axis",
          confine: true, // 限制tooltip在图表区域内
          borderWidth: 0,
          backgroundColor: "rgba(12, 15, 41, 0.9)",
          extraCssText: "box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.5);border-radius: 8px;z-index: 9999;",
          textStyle: {
            fontFamily: "Source Han Sans",
            color: "#FFFFFF",
            fontSize: 14,
          },
          axisPointer: {
            type: "shadow",
            shadowStyle: {
              color: "rgba(64, 158, 255, 0.1)"
            }
          },
          formatter: (params) => {
            const dataIndex = params[0].dataIndex;
            const itemData = this.chartData[dataIndex];
            const completionRate = itemData.expected > 0 ? Math.round((itemData.actual / itemData.expected) * 100) : 0;

            return `${itemData.name}<br/>预期值: ${itemData.expected}<br/>实际值: ${itemData.actual}<br/>完成率: ${completionRate}%`;
          }
        },
        legend: {
          show: true,
          data: ['预期值', '实际值'],
          textStyle: {
            color: '#ACC2E2'
          },
          top: '2%',
          right: '5%'
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "8%",
          top: "15%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          axisLine: {
            show: true,
            lineStyle: {
              color: "rgba(172, 194, 226, 0.5)",
            },
          },
          axisTick: {
            show: true,
          },
          axisLabel: {
            color: "#ACC2E2",
          },
          splitLine: {
            show: false,
          },
        },
        yAxis: {
          type: "category",
          data: categories,
          axisLabel: {
            color: "#ACC2E2",
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "rgba(172, 194, 226, 0.5)",
            },
          },
          axisTick: {
            show: true,
          },
          boundaryGap: true,
        },
        series: [
          // 背景柱子 - 预期值
          {
            name: "预期值",
            type: "bar",
            data: expectedData,
            barCategoryGap: "60%",
            itemStyle: {
              color: "rgba(64, 158, 255, 0.3)", // 浅蓝色背景
              borderRadius: [0, 4, 4, 0]
            },
            emphasis: {
              itemStyle: {
                color: "rgba(64, 158, 255, 0.5)"
              }
            },
            z: 1,
            silent: false,
            barGap: '-100%' // 关键：让柱子重叠
          },
          // 前景柱子 - 实际值
          {
            name: "实际值",
            type: "bar",
            data: actualData,
            barCategoryGap: "60%",
            itemStyle: {
              color: "#409EFF", // 深蓝色前景
              borderRadius: [0, 4, 4, 0]
            },
            emphasis: {
              itemStyle: {
                color: "#66b1ff"
              }
            },
            z: 2,
            silent: false
          }
        ],
      };
      this.mychart.setOption(option);
    },
    handleResize() {
      if (this.mychart) {
        this.mychart.resize();
      }
    },

    // 更新图表数据的方法
    updateChartData(newData) {
      this.chartData = newData;
      if (this.mychart) {
        this.initChart();
      }
    },

    // 获取完成率百分比
    getCompletionRate(actual, expected) {
      return expected > 0 ? Math.round((actual / expected) * 100) : 0;
    },
  },
};
</script>
<style lang="scss" scoped>
.complete-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .chart-box {
    width: 100%;
    flex: 1;
    min-height: 200px; // 减少最小高度，适应容器空间
    max-height: 280px; // 添加最大高度限制
  }
}
</style>
