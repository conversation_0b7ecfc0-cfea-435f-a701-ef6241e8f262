<template>
  <div class="carouselWrapper">
    <div class="arrow left" @click="scrollLeft"></div>
    <div class="carouselBtn" ref="scrollContainer">
      <div
        class="buttonItem"
        :class="{ active: activeIndex === index }"
        @click="clickHandle(index)"
        v-for="(item, index) in buttons"
        :key="index"
      >
        {{ item }}
      </div>
    </div>
    <div class="arrow right" @click="scrollRight"></div>
  </div>
</template>

<script>
export default {
  name: "CarouselBtn",
  props: {
    buttons: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      activeIndex: 0,
    };
  },
  methods: {
    clickHandle(index) {
      this.activeIndex = index;
      this.$emit("clickHandle", index);
    },
    scrollLeft() {
      this.$refs.scrollContainer.scrollBy({ left: -140, behavior: "smooth" });
    },
    scrollRight() {
      this.$refs.scrollContainer.scrollBy({ left: 140, behavior: "smooth" });
    },
  },
};
</script>

<style lang="scss" scoped>
.carouselWrapper {
  display: flex;
  align-items: center;
  padding: 0 10px;
  margin-top: 8px; // 减少顶部边距
  margin-bottom: 4px; // 减少底部边距
  width: 100%; // 自适应
  position: relative;
  box-sizing: border-box;
  flex-shrink: 0; // 防止按钮组被压缩
}

.carouselBtn {
  flex: 1 1 0%; // 占据剩余宽度
  display: flex;
  overflow-x: auto;
  scroll-behavior: smooth;

  &::-webkit-scrollbar {
    display: none;
  }

  .buttonItem {
    margin-right: 8px;
    width: 128px;
    height: 35px;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 16px;
    font-weight: 400;
    flex-shrink: 0; // 不收缩，保证水平排列
  }

  .buttonItem:hover {
    color: #fff !important;
  }
}

.arrow {
  cursor: pointer;
  font-size: 22px;
  padding: 6px 10px;
  user-select: none;
  color: #666;
  flex-shrink: 0;

  &.left {
    margin-right: 4px;
    width: 10px;
    height: 18px;
    background-size: 100% 100%;
  }
  &.right {
    margin-left: 4px;
    background-size: 100% 100%;
  }
}

[data-theme="dark"] .arrow {
  &.left {
    background-image: url("@/assets/tableicon/leftbtn-dark.png");
  }
  &.right {
    background-image: url("@/assets/tableicon/rightbtn-dark.png");
  }
}

[data-theme="tint"] .arrow {
  &.left {
    background-image: url("@/assets/tableicon/leftbtn-tint.png");
  }
  &.right {
    background-image: url("@/assets/tableicon/rightbtn-tint.png");
  }
}

// dark 和 tint 样式保持不变
[data-theme="dark"] .buttonItem {
  background-image: url("@/assets/tableicon/button-darkbg.png");
  color: #6ba4f4;
}
[data-theme="dark"] .active {
  background-image: url("@/assets/tableicon/buttonactive-darkbg.png") !important;
  color: #fff !important;
}
[data-theme="tint"] .buttonItem {
  background-image: url("@/assets/tableicon/button-tintbg.png");
}
[data-theme="tint"] .active {
  background-image: url("@/assets/tableicon/buttonactive-tintbg.png") !important;
  color: #fff !important;
}
</style>
