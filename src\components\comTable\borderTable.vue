<!-- 带边框表格 -->
<template>
  <div class="border-table">
    <el-table
      :data="tableData"
      :border="border"
      v-bind="$attrs"
      :show-summary="showSummary"
    >
      <template v-for="(item, index) in colums">
        <el-table-column
          v-if="item.prop !== 'operation'"
          :key="index"
          v-bind="item"
          :align="item.align ? item.align : 'center'"
          :width="item.width || 'auto'"
        >
          <template v-slot:default="scope" v-if="item.slotName">
            <slot :name="item.slotName" :scope="scope"></slot>
          </template>
          <template v-if="item.children">
            <el-table-column
              v-for="(child, iex) in item.children"
              :key="iex"
              v-bind="child"
              :align="child.align ? child.align : 'center'"
              :width="child.width || 'auto'"
            >
              <template v-slot:default="scope" v-if="child.slotName">
                <slot :name="child.slotName" :scope="scope"></slot>
              </template>
            </el-table-column>
          </template>
        </el-table-column>
      </template>
      <template #empty>
        <el-empty description="暂无数据"></el-empty>
      </template>
    </el-table>
  </div>
</template>
<script>
export default {
  props: {
    tableData: {
      type: Array,
      default: () => [],
    },
    border: {
      type: Boolean,
      default: true,
    },
    colums: {
      type: Array,
      default: () => [],
    },
    showSummary: {
      type: Boolean,
      default: false,
    },
  },
};
</script>
<style scoped>
[data-theme="dark"] .border-table/deep/ .el-table__footer-wrapper tbody td.el-table__cell {
  background-color: #1a2e52;
  color: #fff;
}

[data-theme="tint"] .border-table/deep/ .el-table__footer-wrapper tbody td.el-table__cell {
  background-color: #fff;
  color: #606266;;
}

</style>