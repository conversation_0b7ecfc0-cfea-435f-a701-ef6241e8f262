const changeRouter = {
  path: "/production",
  nameCN: "生产营收",
  component: {
    render: (h) => h("keep-alive", {}, [h("router-view")]),
  },
  children: [
    {
      path: "plan",
      nameCN: "生产计划查询",
      name: "plan",
      component: () => import("@/views/production/prodPlan/index.vue"),
    },
    {
      path: "track",
      nameCN: "生产产量跟踪",
      name: "track",
      component: () => import("@/views/production/prodTrack/index.vue"),
    },
    {
      path: "sales",
      nameCN: "产销配比模型",
      name: "sales",
      component: () => import("@/views/production/prodSales/index.vue"),
    },
    {
      path: "price",
      nameCN: "价格模型",
      name: "price",
      component: () => import("@/views/production/priceSensitivity/index.vue"),
    },
    {
      path: "indicator",
      nameCN: "指标分析",
      name: "indicator",
      component: () =>
        import("@/views/production/prodTrack/indexAnalysis/index.vue"),
    },
  ],
};

export default changeRouter;
