{"name": "dmis", "version": "0.1.0", "private": true, "scripts": {"serve:client": "vue-cli-service serve --host 0.0.0.0", "serve": "vue-cli-service serve", "build": "cross-env WEBPACK_TARGET=node vue-cli-service build && vue-cli-service build --no-clean", "lint": "vue-cli-service lint", "build:client": "vue-cli-service build", "build:server": "cross-env WEBPACK_TARGET=node vue-cli-service build", "start": "node scripts/start", "test:unit": "vue-cli-service test:unit"}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.5", "autossh": "^0.2.0", "axios": "^1.2.6", "cfbgc-design": "^1.0.5", "cfbgc-rem": "^1.0.7", "copy-webpack-plugin": "^6.0.3", "core-js": "^3.4.4", "cross-env": "^7.0.3", "echarts": "^5.6.0", "echarts-gl": "^2.0.9", "element-ui": "^2.15.14", "html2canvas": "^1.4.1", "js-sha1": "^0.6.0", "jsonwebtoken": "^8.5.1", "leaflet": "^1.7.1", "lodash": "^4.17.21", "lodash.merge": "^4.6.2", "mammoth": "^1.9.1", "postcss-px2rem-exclude": "0.0.6", "qiankun": "^2.8.4", "qs": "^6.12.2", "sha1": "^1.1.1", "timers": "^0.1.1", "vue": "^2.7.14", "vue-class-component": "^7.2.6", "vue-element-resize-event": "^0.1.0", "vue-print-nb": "^1.7.5", "vue-property-decorator": "^9.1.2", "vue-router": "^3.6.5", "vue-server-renderer": "^2.7.14", "vue2-editor": "^2.10.3", "vuex": "^3.6.2", "webpack-dev-server": "3.0.0", "webpack-node-externals": "^3.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/chai": "^4.3.4", "@types/mocha": "^10.0.1", "@typescript-eslint/eslint-plugin": "^5.49.0", "@typescript-eslint/parser": "^5.49.0", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-plugin-unit-mocha": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/test-utils": "^1.3.4", "chai": "^4.3.7", "css-loader": "^5.2.7", "eslint": "^8.33.0", "eslint-plugin-vue": "^9.9.0", "less": "^3.0.0", "less-loader": "^6.0.0", "postcss": "^8.0.0", "postcss-import": "^12.0.1", "postcss-loader": "^4.3.0", "postcss-url": "^8.0.0", "sass": "^1.57.1", "sass-loader": "^8.0.0", "single-spa-vue": "^2.5.1", "typescript": "^4.9.4", "vue-template-compiler": "^2.7.14", "webpack": "^5.99.9", "worker-loader": "^3.0.8"}, "browserslist": ["> 1%", "last 2 versions"]}