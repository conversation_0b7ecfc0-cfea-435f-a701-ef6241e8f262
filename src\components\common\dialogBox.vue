<template>
  <div class="dialog-wrapper" v-if="isShow">
    <div class="dialog-mask"></div>
    <div class="dialog-box">
      <div class="info-title">
        <div class="title-text">{{ title }}</div>
        <div class="title-btn" v-if="showBtn" @click="toCER">数据详情</div>
        <div class="title-close" @click="$emit('update:isShow', false)"></div>
      </div>
      <div class="info-content">
        <slot></slot>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "DialogBox",
  props: {
    title: {
      type: String,
    },
    showBtn: {
      type: Boolean,
      default: false,
    },
    isShow: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {};
  },
  methods: {
    toCER() {
      this.$router.push({ name: "costStatistics" });
    }
  },
};
</script>

<style lang="scss" scoped>
.dialog-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
}

.dialog-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.dialog-box {
  z-index: 1001;
  position: relative;
  max-width: 80vw;
  min-width: 860px;
  min-height: 500px;
  top: 50%;
  left: 50%;
  box-sizing: border-box;
  transform: translate(-50%, -50%);

  .info-title {
    width: 100%;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;

    .title-text {
      width: 60%;
      font-family: Source Han Sans;
      font-size: 20px;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-bottom: 10px;
    }

    .title-close {
      width: 32px;
      height: 32px;
      position: fixed;
      top: 12px;
      right: 12px;
      background-size: 100% 100%;
    }

    .title-btn {
      width: 72px;
      height: 32px;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      right: 60px;
      cursor: pointer;

      font-family: Source Han Sans;
      font-size: 14px;
    }
  }

  .info-content {
    margin: 38px 32px 0 32px;
  }
}

[data-theme="dark"] .dialog-box {
  background: #162549;
  border: 1px solid #248eff;

  .title-text {
    background-image: url("@/assets/tableicon/dialogtitle-dark.png");
    background-size: 100% 100%;
  }

  .title-btn {
    background-image: url("@/assets/tableicon/info-darkbtn.png");
    color: #FFFFFF;
  }

  .title-close {
    background-image: url("@/assets/tableicon/close-darkicon.png");
  }
}

[data-theme="tint"] .dialog-box {
  background: #f3f9ff;
  border: 1px solid #59a7ff;

  .title-text {
    background-image: url("@/assets/tableicon/dialogtitle-tint.png");
    background-size: 100% 100%;
  }

  .title-btn {
    background-image: url("@/assets/tableicon/info-tintbtn.png");
  }

  .title-close {
    background-image: url("@/assets/tableicon/close-tinticon.png");
  }
}
</style>