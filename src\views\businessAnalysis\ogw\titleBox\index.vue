<template>
  <div :class="['title-box', greenColor ? 'greenColor' : 'buleColor']" @click="$emit('click')">
    <div class="title-left">
      <div class="title-icon"></div>
      <p class="title-text">{{ title }}</p>
    </div>
    <div class="title-right">
      <span class="title-amount">{{ amount }}</span>
      <span style="
          font-size: 14px;
          font-weight: normal;
          font-family: Source Han Sans;
        ">{{ unit }}</span>
    </div>
  </div>
</template>
<script>
export default {
  name: "TitleBox",
  props: {
    title: {
      type: String,
      default: "",
    },
    amount: {
      type: Number,
      default: "",
    },
    unit: {
      type: String,
      default: "",
    },
    greenColor: {
      type: Boolean,
      default: false
    }
  }
}
</script>
<style lang="scss" scoped>
.title-box {
  margin: 16px;
  border-radius: 4px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .title-left {
    display: flex;
    align-items: center;

    .title-icon {
      width: 40px;
      height: 40px;
      background-size: 100% 100%;
    }

    .title-text {
      font-family: Source <PERSON> Sans;
      font-size: 14px;
      font-weight: normal;
      letter-spacing: normal;
      margin-left: 12px;
    }
  }

  .title-right {
    margin-right: 12px;

    .title-amount {
      font-family: Source Han Sans;
      font-size: 22px;
      font-weight: bold;
      letter-spacing: normal;
      margin-right: 4px;
    }
  }
}

[data-theme="dark"] .buleColor {
  background: #1a2e5e;
  border: 1px solid rgba(23, 131, 255, 0.5);

  .title-icon {
    background-image: url("@/assets/tableicon/medicine-darkicon.png");
  }

  .title-text {
    color: #ffffff;
  }

  .title-right {
    color: #ffffff;
  }
}


[data-theme="dark"] .greenColor {
  background: #153144;
  border: 1px solid rgba(37, 203, 151, 0.5);

  .title-icon {
    background-image: url("@/assets/tableicon/gbudget-darkicon.png");
  }

  .title-text {
    color: #ffffff;
  }

  .title-right {
    color: #1DFBFD;
  }
}



[data-theme="tint"] .buleColor {
  background: #1a2e5e;
  border: 1px solid rgba(23, 131, 255, 0.5);

  .title-icon {
    background-image: url("@/assets/tableicon/medicine-tinticon.png");
  }

  .title-text {
    color: rgba(0, 0, 0, 0.85);
  }

  .title-right {
    color: rgba(0, 0, 0, 0.85);
  }
}


[data-theme="tint"] .greenColor {
  background: #153144;
  border: 1px solid rgba(37, 203, 151, 0.5);

  .title-icon {
    background-image: url("@/assets/tableicon/gbudget-tinticon.png");
  }

  .title-text {
    color: rgba(0, 0, 0, 0.85);
  }

  .title-right {
    color: rgba(0, 0, 0, 0.85);
  }
}
</style>