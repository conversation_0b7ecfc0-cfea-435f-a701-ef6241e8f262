import { instance } from "../config";

// 获取化学药剂筛选列表
export const getFilterChemicals = (data) => {
  return instance({
    url: "/chemicalServe/list",
    method: "post",
    data,
  });
};

// 新增化学药剂
export const saveFilterChemicals = (data) => {
  return instance({
    url: "/chemicalServe/save",
    method: "post",
    data,
  });
};

// 修改化学药剂
export const submitFilterChemicals = (data) => {
  return instance({
    url: "/chemicalServe/submit",
    method: "post",
    data,
  });
};

// 删除化学药剂
export const deleteFilterChemicals = (params) => {
  return instance({
    url: "/chemicalServe/delete",
    method: "post",
    params,
  });
};
