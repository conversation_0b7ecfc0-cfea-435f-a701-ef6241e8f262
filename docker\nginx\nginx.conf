user  nginx;
worker_processes  1;

error_log  /var/log/nginx/error.log warn;
#pid        /var/run/nginx.pid;
pid        /tmp/nginx.pid;



events {
    worker_connections  1024;
}


http {

    proxy_temp_path /tmp/proxy_temp;
    client_body_temp_path /tmp/client_temp;
    fastcgi_temp_path /tmp/fastcgi_temp;
    uwsgi_temp_path /tmp/uwsgi_temp;
    scgi_temp_path /tmp/scgi_temp;
    
    
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;

    gzip  on;
    gzip_buffers 32 4K;
    gzip_comp_level 6;
    gzip_min_length 20;
    gzip_types text/plain application/json text/javascript application/javascript text/css;

    include /etc/nginx/conf.d/*.conf;

    server_tokens off;
}
