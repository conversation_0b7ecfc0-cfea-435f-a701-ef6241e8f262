<template>
  <div class="card-background2">
    <div class="card-background-title" v-if="title">
      {{ title }}
    </div>
    <div class="left-top"></div>
    <div class="right-top"></div>
    <div class="left-bottom"></div>
    <div class="right-bottom"></div>
  </div>
</template>
<script lang="ts">
export default {
  name: "CardBackground2",
  data() {
    return {};
  },
  props: {
    title: {
      default: ''
    }
  }
}
</script>
<style lang="scss" scoped>
.card-background2 {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  border: 1px solid #1694EB;
  box-sizing: border-box;
  .card-background-title {
    width: calc(100% - 40px);
    height: 28px;
    background-image: url('@/assets/images/header-bg5.png');
    background-size: 420px 100%;
    background-repeat: no-repeat;
    font-size: 16px;
    color: #FEB64E;
    text-align: left;
    padding-left: 32px;
    line-height: 28px;
    left: -1px;
    position: relative;
  }

  .left-top {
    border-top: 2px solid #C2FDFE;
    border-left: 2px solid #C2FDFE;
    width: 12px;
    height: 12px;
    position: absolute;
    top: -2px;
    left: -2px;
  }

  .right-top {
    border-top: 2px solid #C2FDFE;
    border-right: 2px solid #C2FDFE;
    width: 12px;
    height: 12px;
    position: absolute;
    top: -2px;
    right: -2px;
  }

  .left-bottom {
    border-bottom: 2px solid #C2FDFE;
    border-left: 2px solid #C2FDFE;
    width: 12px;
    height: 12px;
    position: absolute;
    bottom: -2px;
    left: -2px;
  }

  .right-bottom {
    border-bottom: 2px solid #C2FDFE;
    border-right: 2px solid #C2FDFE;
    width: 12px;
    height: 12px;
    position: absolute;
    bottom: -2px;
    right: -2px;
  }
}
</style>