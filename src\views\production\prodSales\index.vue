<template>
  <div class="production-sales-model">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <h1>产销配比模型</h1>
        <div style="float: right;margin-top: -30px;">
          选择模型：
          <el-select v-model="modelType" placeholder="请选择">
            <el-option label="产销配比模型1" value="1"></el-option>
            <el-option label="产销配比模型2" value="2"></el-option>
          </el-select>
        </div>
      </div>

      <!-- 基础参数设定 -->
      <el-card shadow="hover" class="section-card">
        <div slot="header">
          <h2>基础参数设定</h2>
        </div>
        <el-form :model="form" :inline="true" label-position="left">
          <el-form-item label="产量单位：">
            <el-input
              v-model="form.productionUnit"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="计划产量目标：">
            <el-input
              v-model="form.productionTarget"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-form>
      </el-card>

      <div class="section-box">
        <!-- 终端销售渠道及价格 -->
        <el-card shadow="hover" class="section-card card-left">
          <div slot="header">
            <h2>终端销售渠道及价格</h2>
          </div>
          <el-table :data="salesChannels" border style="width: 100%">
            <el-table-column
              type="index"
              label="序号"
              width="100"
            ></el-table-column>
            <el-table-column
              prop="channel"
              label="渠道"
              width="180"
            ></el-table-column>
            <el-table-column label="价格">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.price"
                  placeholder="请输入"
                ></el-input>
              </template>
            </el-table-column>
            <el-table-column label="膨胀系数">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.expansionCoefficient"
                  placeholder="请输入"
                ></el-input>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 渠道配置 -->
        <el-card shadow="hover" class="section-card card-right">
          <div slot="header">
            <h2>渠道配置</h2>
          </div>
        </el-card>
      </div>

      <!-- 约束条件配置 -->
      <el-card shadow="hover" class="section-card">
        <div slot="header">
          <h2>约束条件配置</h2>
        </div>

        <div class="section-box">
          <div class="card-left">
            <h3>渠道供应策略</h3>
            <el-table :data="supplyStrategy" border style="width: 100%">
              <el-table-column
                type="index"
                label="序号"
                width="100"
              ></el-table-column>
              <el-table-column
                prop="channel"
                label="渠道"
                width="180"
              ></el-table-column>
              <el-table-column label="最小供应量">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.minSupply"
                    placeholder="请输入"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="最大接收量">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.maxReceive"
                    placeholder="请输入"
                  ></el-input>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="card-right">
            <h3>气田产量约束</h3>
            <el-table :data="fieldConstraints" border style="width: 100%">
              <el-table-column
                type="index"
                label="序号"
                width="100"
              ></el-table-column>
              <el-table-column
                prop="field"
                label="气田"
                width="180"
              ></el-table-column>
              <el-table-column label="气田最低产量">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.minProduction"
                    placeholder="请输入"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="气田最高产量">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.maxProduction"
                    placeholder="请输入"
                  ></el-input>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-card>

      <div class="action-buttons" v-if="!isCalculating">
        <el-button type="primary" size="large" @click="startCalculation"
          >开始测算</el-button
        >
      </div>

      <el-card shadow="hover" class="section-card" v-else>
        <div slot="header" class="card-header">
          <h2>测算结果</h2>
          <el-button type="primary" @click="resetCalculation">重置</el-button>
        </div>
        <div class="section-box">
          <div class="card-left">
            <el-table
              :data="resultData"
              border
              style="width: 100%"
              show-summary
            >
              <el-table-column
                v-for="item in columns"
                :key="item.channel"
                :prop="item.prop"
                :label="item.label"
              />
            </el-table>
          </div>

          <div class="card-right">
            <div class="result-title">
              <div class="result-title-text">最大销售额</div>
              <div class="result-title-formula">
                Z=P1X31 +P2(X32+X42)+P3(X13+X23+X33)
              </div>
            </div>
            <div class="chart-box">
              <SalesVolume></SalesVolume>
            </div>
          </div>
        </div>
      </el-card>
    </el-card>
  </div>
</template>

<script>
import SalesVolume from "./salesVolume.vue";

export default {
  name: "prodSales",
  components: {
    SalesVolume,
  },
  mounted() {
    window.document.documentElement.setAttribute("data-theme", "tint");
  },
  data() {
    return {
      modelType:"",
      isCalculating: false,
      form: {
        productionUnit: "",
        productionTarget: "",
      },
      salesChannels: [
        { id: 1, channel: "香港中电", price: "", expansionCoefficient: "" },
        { id: 2, channel: "气电南山", price: "", expansionCoefficient: "" },
        { id: 3, channel: "气电广东", price: "", expansionCoefficient: "" },
      ],
      channels: ["崖城13-1", "崖城13-10", "陵水17-2", "陵水25-1"],
      selectedChannels: [],
      demandCenters: ["气电南山", "气电广东"],
      selectedDemandCenters: [],
      supplyStrategy: [
        { id: 1, channel: "香港中电", minSupply: "", maxReceive: "" },
        { id: 2, channel: "气电南山", minSupply: "", maxReceive: "" },
        { id: 3, channel: "气电广东", minSupply: "", maxReceive: "" },
      ],
      fieldConstraints: [
        { id: 1, field: "唐城13-1", minProduction: "", maxProduction: "" },
        { id: 2, field: "唐城13-10", minProduction: "", maxProduction: "" },
        { id: 3, field: "陵水17-2", minProduction: "", maxProduction: "" },
        { id: 4, field: "陵水25-1", minProduction: "", maxProduction: "" },
      ],
      columns: [
        { label: "", prop: "channel" },
        { label: "崖城13-1", prop: "yancheng13_1" },
        { label: "崖城13-10", prop: "yancheng13_10" },
        { label: "陵水17-2", prop: "lingshui17_2" },
        { label: "陵水25-1", prop: "lingshui25_1" },
        { label: "总计", prop: "totalSales" },
      ],
      resultData: [
        {
          channel: "香港中电",
          yancheng13_1: "154",
          yancheng13_10: "256",
          lingshui17_2: "369",
          lingshui25_1: "485",
          totalSales: "1264",
        },
        {
          channel: "气电南山",
          yancheng13_1: "123",
          yancheng13_10: "456",
          lingshui17_2: "789",
          lingshui25_1: "321",
          totalSales: "1689",
        },
        {
          channel: "气电广东",
          yancheng13_1: "654",
          yancheng13_10: "987",
          lingshui17_2: "321",
          lingshui25_1: "654",
          totalSales: "2616",
        },
      ],
    };
  },
  methods: {
    startCalculation() {
      // 这里可以添加开始测算的逻辑
      this.$message.success("开始测算...");
      this.isCalculating = true;
    },
    resetCalculation() {
      this.isCalculating = false;
      // ToDo: 清空表单
    },
  },
};
</script>

<style scoped>
.production-sales-model {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-card {
  margin-bottom: 20px;
}

.section-box {
  display: flex;
}

.card-left {
  flex: 1;
  margin-right: 12px;
}

.card-right {
  flex: 1;
}

.action-buttons {
  text-align: center;
  margin-top: 30px;
  margin-bottom: 20px;
}
.result-title {
  display: flex;
  align-items: center;
  width: 100%;
  background: #ecf7ff;
  border-radius: 4px;
  box-sizing: border-box;
  border: 1px solid #1677ff;
}
.result-title-text {
  width: 100px;
  height: 26px;
  font-family: Source Han Sans;
  font-size: 14px;
  color: #fff;
  background: #1677ff;
  text-align: center;
}
.result-title-formula {
  color: #1677ff;
  font-family: Source Han Sans;
  font-size: 14px;
  margin-left: 20%;
}

</style>
