<template>
  <div class="year-on-year">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  name: "YearOnyear",
  data() {
    return {
      xData: [],
      yData: [],
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  methods: {
    initChart() {
      let mychart = echarts.init(this.$refs.chartBox);
      this.option = {
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: ["YC-1", "YC-2"], //当前装置
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          //   boundaryGap: false,
          //   data: this.xdata, //表格月份
          data: [
            "1月",
            "2月",
            "3月",
            "4月",
            "5月",
            "6月",
            "7月",
            "8月",
            "9月",
            "10月",
            "11月",
            "12月",
          ],
          axisTick: {
            show: false,
          },
          axisLabel: {
            interval: 0,
            rotate: 30,
            fontSize: 10,
          },
        },

        yAxis: [
          {
            type: "value",
            splitLine: {
              lineStyle: {
                color: "rgba(172, 194, 226, 0.2)",
              },
            },
          },
        ],
        series: [
          {
            name: "YC-1",
            type: "line",
            data: [
              "11.12",
              "15.75",
              "20",
              "23.15",
              "25.5",
              "27.5",
              "29.5",
              "31.5",
              "33.5",
              "35.5",
              "37.5",
              "39.5",
            ],
          },
          {
            name: "YC-2",
            type: "line",
            data: [
              "21.12",
              "25.75",
              "13",
              "17.15",
              "22.5",
              "27.5",
              "18.5",
              "23.5",
              "33.5",
              "26.5",
              "30.5",
              "19.5",
            ],
          },
        ],
      };
      mychart.setOption(this.option);
    },
  },
};
</script>
<style lang="scss" scoped>
.year-on-year {
  width: 100%;
  .chart-box {
    width: 90%;
    height: 320px;
  }
}
</style>
