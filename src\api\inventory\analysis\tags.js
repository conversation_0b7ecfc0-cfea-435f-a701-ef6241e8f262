
import {instance} from "../../config";
// 标签分页列表查询
export function sapInfoTag_list(params) {
    return instance({
        url: '/im-ls/sapInfoTag/pageList',
        method: "GET",
        params
    });
}

/*新增标签 编辑*/
export function sapInfoTag_add_edit(data) {
    return instance({
        url: '/im-ls/sapInfoTag',
        method: "post",
        data
    });
}
/*删除*/
export function sapInfoTag_del(params) {
    return instance({
        url: '/im-ls/sapInfoTag/remove',
        method: "POST",
        params
    });
}


/*标签列表查询-不分页*/
export function sapInfoTag_nopage(params) {
    return instance({
        url: '/im-ls/sapInfoTag/list',
        method: "get",
        params
    });
}
