<template>
  <div class="header-item">
    <div class="title-left">
      <div class="budget-icon"></div>
      <p class="title-text">月累计估算</p>
    </div>
    <div>
      <span class="amount">50</span>
      <span class="unit">万元</span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.header-item {
  box-sizing: border-box;
  width: 259px;
  height: 64px;
  border-radius: 6px;
  background: #1a2e5e;
  box-sizing: border-box;
  border: 2px solid;
  border-image: linear-gradient(180deg, #1783ff 0%, rgba(23, 131, 255, 0) 100%)
    2;
  display: flex;
  align-items: center;
  justify-content: space-around;

  .title-left {
      display: flex;
      align-items: center;
      .budget-icon {
        width: 48px;
        height: 48px;
        background-size: 100% 100%;
        margin-right: 8px;
      }
      .title-text {
        font-family: 思源黑体;
        font-size: 16px;
        font-weight: normal;
        letter-spacing: normal;
      }
    }
    .amount {
      font-family: D-DIN;
      font-size: 32px;
      font-weight: bold;
      letter-spacing: normal;
      color: #1dfbfd;
    }
    .unit {
      margin-left: 4px;
      font-family: Source Han Sans;
      font-size: 14px;
      font-weight: normal;
      letter-spacing: normal;
      color: #1dfbfd;
    }
}

[data-theme="dark"] .budget-icon {
  background-image: url("@/assets/tableicon/budget-darkicon.png");
}
[data-theme="tint"] .budget-icon {
  background-image: url("@/assets/tableicon/budget-tinticon.png");
}
[data-theme="dark"] .title-text {
  color: #fff;
}
[data-theme="tint"] .title-text {
  color: rgba(0, 0, 0, 0.85);
}
</style>