<template>
  <div class="sales-volume">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  name: "SalesVolume",

  data() {
    return {
      xData: [],
      yData: [],
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  methods: {
    initChart() {
      let mychart = echarts.init(this.$refs.chartBox);
      this.option = {
        tooltip: {
          trigger: "item",
        },
        legend: {
          data: ["YC13-1", "YC13-10", "LS17-2", "LS25-1"], //当前装置
          bottom: "bottom",
        },
        grid: {
          top: "3%",
          left: "3%",
          right: "4%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: ["-10%", "-5%", "0%", "5%", "10%"],
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            name: "YC13-1",
            type: "line",
            data: ["20", "94", "100", "100", "100"],
          },
          {
            name: "YC13-10",
            type: "line",
            data: ["62", "94", "150", "120", "100"],
          },
        ],
      };
      mychart.setOption(this.option);
    },
  },
};
</script>
<style lang="scss" scoped>
.sales-volume {
  width: 100%;
  .chart-box {
    width: 90%;
    height: 320px;
  }
}
</style>
