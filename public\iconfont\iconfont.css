@font-face {
  font-family: "iconfont"; /* Project id 4970339 */
  src: url('iconfont.woff2?t=1752028432948') format('woff2'),
       url('iconfont.woff?t=1752028432948') format('woff'),
       url('iconfont.ttf?t=1752028432948') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-opinionActive:before {
  content: "\e887";
}

.icon-applyActive:before {
  content: "\e888";
}

.icon-opinion:before {
  content: "\e885";
}

.icon-apply:before {
  content: "\e76a";
}

.icon-file:before {
  content: "\e886";
}

.icon-fileActive:before {
  content: "\e889";
}

