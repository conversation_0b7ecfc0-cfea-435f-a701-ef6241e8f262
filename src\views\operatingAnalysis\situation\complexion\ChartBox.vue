<template>
  <div class="reProsRate-container">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  name: "reProsRate",
  data() {
    return {
      xData: ["2021年", "2022年", "2023年", "2024年", "2025年"],
      yData: [6.3, 4.5, 4, 7, 8.4],
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  methods: {
    initChart() {
      let mychart = echarts.init(this.$refs.chartBox);
      this.option = {
        color: ["#248EFF"], //圆柱体颜色
        tooltip: {
          trigger: "item",
          padding: 1,
          formatter: function (param) {},
        },
        legend: {
          data: ["储采比", "采出程度"],
          textStyle: {
            color: "#ffffff",
            fontSize: 12,
          },
          icon: "rect",
          itemWidth: 8,
          itemHeight: 8,
          right: "10%",
          top: "2%",
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: this.xData,
            axisTick: {
              alignWithLabel: true,
            },
            nameTextStyle: {
              color: "#ACC2E2", //文本颜色
            },
            axisLine: {
              lineStyle: {
                color: "rgba(172, 194, 226, 0.2)", //轴线颜色
              },
            },
            axisLabel: {
              textStyle: {
                color: "#ACC2E2", //轴线文本颜色
              },
            },
          },
        ],
        yAxis: {
          type: "value",
          splitLine: {
            show: false,
          },
          axisLine: {
            show: true, // 显示轴线
            lineStyle: {
              color: "rgba(172, 194, 226, 0.2)", // 轴线颜色
              width: 1, // 轴线宽度
            },
          },
          axisLabel: {
            textStyle: {
              color: "#ACC2E2", //轴线文本颜色
            },
          },
        },
        series: [
          {
            name: "储采比",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [0, -5],
            symbolPosition: "end",
            z: 12,
            label: {
              normal: {
                show: false,
              },
            },
            data: this.yData,
          },
          {
            name: "储采比",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [0, 5],
            z: 12,
            data: this.yData,
          },
          {
            name: "储采比",
            type: "bar",
            itemStyle: {
              normal: {
                opacity: 0.7,
              },
            },
            barWidth: "20",
            data: this.yData,
          },
          {
            name: "采出程度",
            type: "line",
            data: [4.2, 5, 2.3, 5, 7],
            itemStyle: {
              color: "#FF6660", // 设置线条颜色为红色
            },
          },
        ],
      };
      mychart.setOption(this.option);
    },
  },
};
</script>
<style lang="scss" scoped>
.reProsRate-container {
  width: 100%;
  height: 100%;
  .chart-box {
    width: 100%;
    height: 300px;
  }
}
</style>
