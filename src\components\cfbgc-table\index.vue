<template>
    <div class="cfbgc-table">
        <cf-spin :spinning="!tableShow">
            <cf-table
                :columns="columnsClient"
                :data-source="dataSourceClient"
                v-if="tableShow"
                :pagination="pagination"
                :scroll="{y: scroll.y* cfbgcRem.globalRem}"
            >
                <!--                <a slot="action" slot-scope="text">action</a>-->
            </cf-table>
        </cf-spin>
    </div>
</template>
<script>
export default {
    name: 'cfbgcTable',
    data() {
        return {
            columnsClient: [],
            dataSourceClient: [],
            tableShow: true
        };
    },
    props: {
        scroll: {
            type: Object,
            default() {
                return {}
            }
        },
        pagination: {
            type: Object | Boolean,
            default() {
                return false;
            }
        },
        columns: {
            type: Array,
            default() {
                return [
                    {title: 'Full Name', width: 100, dataIndex: 'name', key: 'name', fixed: 'left'},
                    {title: 'Age', width: 100, dataIndex: 'age', key: 'age'},
                    {title: 'Column 1', dataIndex: 'address', key: '1', width: 150},
                    {title: 'Column 2', dataIndex: 'address1', key: '2', width: 150},
                    {title: 'Column 3', dataIndex: 'address2', key: '3', width: 150},
                    {title: 'Column 4', dataIndex: 'address3', key: '4', width: 150},
                    {
                        title: 'Action',
                        key: 'operation',
                        fixed: 'right',
                        width: 100,
                        scopedSlots: {customRender: 'action'},
                    },
                ]
            }
        },
        dataSource: {
            type: Array,
            default() {
                return [
                    {
                        key: 1,
                        name: `Edrward 1`,
                        age: 32,
                        address: `London Park no. 1`,
                    },
                    {
                        key: 2,
                        name: `Edrward 1`,
                        age: 32,
                        address: `London Park no. 1`,
                    },
                    {
                        key: 3,
                        name: `Edrward 1`,
                        age: 32,
                        address: `London Park no. 1`,
                    }
                ]
            }
        }
    },
    watch: {
        columns: {
            handler() {
                this.initColumnCient()
            },
            deep: true
        },
        dataSource: {
            handler() {
                this.initDatasourceCient()
            },
            deep: true
        }
    },
    created() {
        this.init();
    },
    mounted() {
        this.$nextTick()
            .then(() => {
                this.remListen()
            })
    },
    methods: {
        remListen() {
            this.cfbgcRem.pushSizeChange(() => {
                this.tableShow = false;
                this.$nextTick()
                    .then(() => {
                        this.tableShow = true
                    })
            })
        },
        init() {
            this.initColumnCient();
            this.initDatasourceCient();
        },
        initColumnCient() {
            this.columnsClient = this.columns;
        },
        initDatasourceCient() {
            this.dataSourceClient = this.dataSource;
        }
    }
};
</script>
<style lang="scss">
.cfbgc-table {
    height: 100%;
    overflow: auto;
    position: relative;

    .spin {
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 6;
    }

    // 重写
    .ant-table-header.ant-table-hide-scrollbar {
        overflow: hidden !important;
        margin: 0!important;
    }

    .ant-table-thead > tr > th {
        border-bottom: 0;
    }

    .ant-table-fixed-header > .ant-table-content > .ant-table-scroll > .ant-table-body{
        background: rgba(0,0,0,0);
        border: 1px solid #2e4c85;
        border-width: 0 1px 1px 1px;
    }

    .ant-table-placeholder{
        border: 1px solid #2e4c85;
    }

    .ant-table-tbody {
        & > tr {
            & > td {
                border-bottom: 1px solid #2e4c85;
            }
        }
    }
}
</style>
