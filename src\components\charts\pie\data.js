// 生成扇形的曲面参数方程，用于 series-surface.parametricEquation
function getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, h) {

    // 计算
    let midRatio = (startRatio + endRatio) / 2;

    let startRadian = startRatio * Math.PI * 2;
    let endRadian = endRatio * Math.PI * 2;
    let midRadian = midRatio * Math.PI * 2;

    // 如果只有一个扇形，则不实现选中效果。
    if (startRatio === 0 && endRatio === 1) {
        isSelected = false;
    }

    // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
    k = typeof k !== 'undefined' ? k : 1 / 3;

    // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
    let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
    let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;

    // 计算高亮效果的放大比例（未高亮，则比例为 1）
    let hoverRate = isHovered ? 1.05 : 1;

    // 返回曲面参数方程
    return {

        u: {
            min: -Math.PI,
            max: Math.PI * 3,
            step: Math.PI / 32
        },

        v: {
            min: 0,
            max: Math.PI * 2,
            step: Math.PI / 20
        },

        x: function (u, v) {
            if (u < startRadian) {
                return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
            }
            if (u > endRadian) {
                return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
            }
            return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
        },

        y: function (u, v) {
            if (u < startRadian) {
                return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
            }
            if (u > endRadian) {
                return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
            }
            return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
        },

        z: function (u, v) {
            if (u < -Math.PI * 0.5) {
                return Math.sin(u);
            }
            if (u > Math.PI * 2.5) {
                return Math.sin(u) * h * .1;
            }
            return Math.sin(v) > 0 ? 1 * h * .1 : -1;
        }
    };
}



// 生成模拟 3D 饼图的配置项
function getPie3D(pieData, internalDiameterRatio,position) {
    let series = [];
    let sumValue = 0;
    let startValue = 0;
    let endValue = 0;
    let legendData = [];
    let k = typeof internalDiameterRatio !== 'undefined' ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio) : 1 / 3;

    // 处理空数据的情况
    if (!pieData || pieData.length === 0) {
        return {
            legend: {
                show: false
            },
            tooltip: {
                show: false
            },
            series: []
        };
    }

    // 为每一个饼图数据，生成一个 series-surface 配置
    for (let i = 0; i < pieData.length; i++) {

        sumValue += pieData[i].value;

        let seriesItem = {
            name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
            type: 'surface',
            parametric: true,
            wireframe: {
                show: false
            },
            pieData: pieData[i],
            pieStatus: {
                selected: false,
                hovered: false,
                k: k
            },
            // 为3D图表添加tooltip配置
            tooltip: {
                show: true
            }
        };

        if (typeof pieData[i].itemStyle != 'undefined') {

            let itemStyle = {};

            typeof pieData[i].itemStyle.color != 'undefined' ? itemStyle.color = pieData[i].itemStyle.color : null;
            typeof pieData[i].itemStyle.opacity != 'undefined' ? itemStyle.opacity = pieData[i].itemStyle.opacity : null;

            seriesItem.itemStyle = itemStyle;
        }
        series.push(seriesItem);
    }

    // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
    // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
    for (let i = 0; i < series.length; i++) {
        endValue = startValue + series[i].pieData.value;

        series[i].pieData.startRatio = startValue / sumValue;
        series[i].pieData.endRatio = endValue / sumValue;
        series[i].parametricEquation = getParametricEquation(series[i].pieData.startRatio, series[i].pieData.endRatio, false, false, k, series[i].pieData.value);

        startValue = endValue;

        legendData.push(series[i].name);

    }

    // 处理图例
    if (legendData.length > 0) {
        // console.log(legendData, 'legendData')
        legendData.map((item, index) => {
            legendData[index] = {
                name: item,
                textStyle: {
                    color: '#fff'
                }
            }
        })
    }

    // 补充一个透明的圆环，用于支撑高亮功能的近似实现。
    series.push({
        name: 'mouseoutSeries',
        type: 'surface',
        parametric: true,
        wireframe: {
            show: false
        },
        itemStyle: {
            opacity: 0
        },
        parametricEquation: {
            u: {
                min: 0,
                max: Math.PI * 2,
                step: Math.PI / 20
            },
            v: {
                min: 0,
                max: Math.PI,
                step: Math.PI / 20
            },
            x: function (u, v) {
                return Math.sin(v) * Math.sin(u) + Math.sin(u);
            },
            y: function (u, v) {
                return Math.sin(v) * Math.cos(u) + Math.cos(u);
            },
            z: function (u, v) {
                return Math.cos(v) > 0 ? 0.1 : -0.1;
            }
        }
    });
    // 准备待返回的配置项，把准备好的 legendData、series 传入。
    let option = {
        //animation: false,
        legend: {
            selectedMode:false,
            show: true,
            data: legendData,
            ...position,
            formatter: function (name) {
                // 找到对应的数据项
                let targetSeries = series.find(s => s.name === name);
                if (targetSeries && targetSeries.pieData) {
                    let rateValue = (targetSeries.pieData.value / sumValue * 100).toFixed(0);
                    if(isNaN(rateValue)){
                        rateValue = 0;
                    }
                    return `${name} ${rateValue}%`;
                }
                return name;
            }
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        tooltip: {
            show: true,
            trigger: 'item',
            confine: true, // 限制tooltip在图表区域内
            backgroundColor: 'rgba(12, 15, 41, 0.9)',
            borderWidth: 0,
            textStyle: {
                color: '#FFFFFF',
                fontSize: 14,
                fontFamily: 'Source Han Sans'
            },
            extraCssText: 'box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.5);border-radius: 8px;z-index: 9999;',
            formatter: params => {
                console.log('Tooltip params:', params); // 调试信息

                // 修复tooltip显示问题 - 对于3D图表，params可能有不同的结构
                if (params && params.seriesName && params.seriesName !== 'mouseoutSeries') {
                    // 从series数组中查找对应的数据项
                    let targetSeries = series.find(s => s.name === params.seriesName);
                    if (targetSeries && targetSeries.pieData && targetSeries.pieData.value !== undefined) {
                        let value = targetSeries.pieData.value;
                        let percentage = sumValue > 0 ? ((value / sumValue) * 100).toFixed(1) : 0;

                        console.log(`显示tooltip: ${params.seriesName}, 数值: ${value}, 占比: ${percentage}%`);

                        return `
                            <div style="padding: 8px; min-width: 120px;">
                                <div style="font-weight: bold; margin-bottom: 4px; color: #FFFFFF;">${params.seriesName}</div>
                                <div style="display: flex; align-items: center; margin-bottom: 2px;">
                                    <span style="display:inline-block;margin-right:8px;border-radius:50%;width:8px;height:8px;background-color:${params.color || targetSeries.itemStyle?.color || '#4F42B9'};"></span>
                                    <span style="color: #FFFFFF;">数值: ${value}万元</span>
                                </div>
                                <div style="margin-left: 16px; color: #FFFFFF;">占比: ${percentage}%</div>
                            </div>
                        `;
                    }
                }
                return '';
            }
        },
        xAxis3D: {
            min: -1,
            max: 1
        },
        yAxis3D: {
            min: -1,
            max: 1
        },
        zAxis3D: {
            min: -1,
            max: 1
        },
        grid3D: {
            show: false,
            boxHeight: 10,
            viewControl: {//3d效果可以放大、旋转等，请自己去查看官方配置
                alpha: 40,
                // beta: 40,
                rotateSensitivity: 0,
                zoomSensitivity: 0,
                panSensitivity: 0,
                autoRotate: false
            },
            //后处理特效可以为画面添加高光、景深、环境光遮蔽（SSAO）、调色等效果。可以让整个画面更富有质感。
            postEffect: {//配置这项会出现锯齿，请自己去查看官方配置有办法解决 
                enable: true,
                bloom: {
                    enable: true,
                    bloomIntensity: 0.1
                },
                SSAO: {
                    enable: true,
                    quality: 'medium',
                    radius: 2
                }
            }
        },
        series: series
    };
    return option;
}

export {
    getParametricEquation,
    getPie3D
}