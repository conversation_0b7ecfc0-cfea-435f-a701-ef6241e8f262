<template>
    <div id="risklevel-wrap-echart" style="width: 100%;height: 100%;"></div>
</template>

<script>
    import * as echarts from "echarts";
    import {sapInfoDay_safeDistr} from "../api/inventory/analysis/materialSearch";

    export default {
        name:'EchartCompany',
        props: ['company','dataTime'],
        data() {
            return {
                option: {},
                chartDom: {},
                myChart: {}
            }
        },
        mounted() {
            this.fengxiandengjiwrap()
        },
        computed: {
            color_font() {
                return this.$store.state.curTheme == 0 ? 'black' : '#fff'
            }
        },
        watch: {
            dataTime() {
                this.fengxiandengjiwrap()
            },
            company() {
                this.fengxiandengjiwrap()
            },
            color_font() {
                this.option.legend.textStyle.color = this.color_font
                this.option.title.textStyle.rich.name.color = this.color_font
                this.myChart && this.myChart.setOption(this.option);
            }
        },
        methods: {
            async fengxiandengjiwrap() {
                const query = {
                    dataTime: this.dataTime,
                    company: this.company
                }
                const res = await sapInfoDay_safeDistr(query)
                if (res.code == 200) {
                    let chartData = []
                    res.data.distributionVOS.forEach((item) => {
                        item.name = item.lable
                    })
                    chartData = res.data.distributionVOS
                    this.chartDom = document.getElementById('risklevel-wrap-echart');
                    this.myChart = echarts.init(this.chartDom);
                    let total = res.data.sum;
                    this.option = {
                        tooltip: {
                            textStyle: {
                                align: 'left', //提示文字左对齐
                            },
                            trigger: 'item',
                            formatter: function (data) {
                                let result = ''
                                if (chartData && chartData.length !== 0) {
                                    chartData.forEach(item => {
                                        if (item.name == data.name) {
                                            result = `<div>
                                                <span>${item.name}</span>
                                                <span>${item.value}（万元）</span>
                                             </div>`
                                        }

                                    })
                                }
                                return result
                            },
                        },
                        title: {
                            zlevel: 0,
                            text: [
                                '{value|' + total + '}',
                                '{name|总库存}',
                            ].join('\n'),
                            top: 'center',
                            left: 'center',
                            textStyle: {
                                rich: {
                                    value: {
                                        color: '#2C64BD',
                                        fontSize: 24,
                                        fontWeight: 'bold',
                                        lineHeight: 31,
                                    },
                                    name: {
                                        color: this.color_font,
                                        lineHeight: 14
                                    },
                                },
                            },
                        },
                        legend: {
                            align: 'left', //图例在左，文字在右
                            orient: 'vertical',
                            icon: 'circle',
                            top: 'middle',
                            right: '100',
                            formatter(name) {
                                const item = chartData.filter((item) => item.name === name)[0];
                                return `${name} ${item.value} （万元）`;
                            },
                            textStyle: {
                                color: this.color_font,
                                fontSize: 14,
                            },
                            color: ['#01D7E2', '#F5A025', '#FF7723'],
                        },
                        grid: {

                            containLabel: true,
                        },
                        series: [
                            {
                                type: 'pie',
                                center: ['50%', '50%'],
                                radius: ['40%', '60%'],
                                avoidLabelOverlap: false,
                                color: ['#01D7E2', '#F5A025', '#FF7723'],
                                label: {
                                    show: false,
                                    position: 'center'
                                },
                                emphasis: {
                                    label: {
                                        color: this.color_font,
                                        show: false,
                                        fontSize: '40',
                                        fontWeight: 'bold'
                                    }
                                },
                                labelLine: {
                                    show: false
                                },
                                data: chartData
                            }
                        ]
                    };
                    this.myChart.setOption(this.option);
                }
            }
        }
    }
</script>

<style lang="scss">
    .risklevel-title {
        width: 100%;
        height: 1.6823rem;
        background: linear-gradient(270deg, #073172 0%, rgba(0, 97, 176, 0.5) 100%);
        font-size: .8906rem;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #FFFFFF;
        line-height: .7422rem;
        display: flex;
        align-items: center;

        .risklevel-arrow {
            margin: 0 .475rem 0 .2672rem;
        }
    }

    .screen-bottom_left {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 0;
        height: 0;
        border-color: transparent #00B7F2;
        border-width: .2969rem 0px 0px .2969rem;
        border-style: solid;
    }

    .screen-bottom-right {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 0;
        height: 0;
        border-color: transparent #00B7F2;
        border-width: .2969rem .2969rem 0px 0px;
        border-style: solid;

    }

    .risklevel {
        width: 14.9431rem;
        height: 15.24rem;
        background: rgba(3, 11, 25, 0.05);
        border: 1px solid rgba(0, 183, 242, 0.2);

        #risklevel-wrap-echart {
            width: 100%;
            height: calc(100% - 1.6823rem)
        }
    }
</style>
