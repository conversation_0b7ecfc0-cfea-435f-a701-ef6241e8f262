#**********************************************************#
#          ====== template ======               #
#**********************************************************#


#**********************************************************#
#                      == Image ==                         #
#**********************************************************#

image:
  repository: harbor.cfbgcfront.com/PROJECT_CODE/CI_PROJECT_NAME
  tag: IMAGE_TAG
  pullPolicy: IfNotPresent

replicas: 1

imagePullSecrets: [{"name":"secret-nexus"}]
restartPolicy: Always

#**********************************************************#
#                       == Env ==                         #
#**********************************************************#

# env:
#   - name: SPRING_PROFILES_ACTIVE
#     valueFrom:
#       configMapKeyRef:
#         name: envconfig
#         key: SPRING_PROFILES_ACTIVE

## ========== env配置示例 ========== #####
# env:
#   - name: "JAVA_OPTS"
#     value: "-Dserver.port=8080"
#   - name: "APP_OPTS"
#     value: ""

#**********************************************************#
#                       == Port ==                         #
#**********************************************************#

ports:
  - name: server
    containerPort: 8080
    protocol: TCP

#**********************************************************#
#                   == PodAnnotations ==                   #
#**********************************************************#

podAnnotations: {}

## ========== readinessProbe配置示例 ========== #####
# podAnnotations：
#   prometheus.io/scrape: "true"
#   prometheus.io/port: "9030"
#   prometheus.io/path: "/"

#**********************************************************#
#                      == Probe ==                         #
#**********************************************************#

livenessProbe: []
readinessProbe: []

## ========== livenessProbe配置示例 ========== #####
# livenessProbe:
#   httpGet:
#     path: /
#     port: http
## ========== readinessProbe配置示例 ========== #####
# readinessProbe:
#   httpGet:
#     path: /
#     port: http

#**********************************************************#
#                 == serviceAccount ==                     #
#**********************************************************#

serviceAccountName: {}

## ========== serviceAccount配置示例 ========== #####
# serviceAccountName: admin

#**********************************************************#
#                  == podSecurity ==                       #
#**********************************************************#

podSecurityContext: {}
securityContext: {}

  ## ========== podSecurityContext配置示例 ========== #####
  # podSecurityContext:
  # fsGroup: 2000
  ## ========== securityContext配置示例 ========== #####
  # securityContext:
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
# runAsUser: 1000

#**********************************************************#
#                    == Resources ==                       #
#**********************************************************#

resources:
  limits:
    cpu: 500m
    memory: 500Mi
  requests:
    cpu: 50m
    memory: 200Mi


#**********************************************************#
#                   == NodeSelector ==                     #
#**********************************************************#

nodeSelector: {}

## ========== tNodeSelector配置示例 ========== #####
# nodeSelector:
#   kubernetes.io/hostname: k8s-node-2-13

#**********************************************************#
#                    == Tolerations ==                     #
#**********************************************************#

tolerations: []

## ========== tolerations配置示例 ========== #####
# tolerations:
#   - operator: Exists

#**********************************************************#
#                      == Affinity ==                      #
#**********************************************************#

affinity: {}

## ========== node affinity配置示例 ========== #####
# affinity:
#   nodeAffinity:
#     requiredDuringSchedulingIgnoredDuringExecution:
#       nodeSelectorTerms:
#         - matchExpressions:
#             - key: kubernetes.io/hostname
#               operator: In
#               values:
#                 - k8s-node-2-12
#     preferredDuringSchedulingIgnoredDuringExecution:
#       - weight: 1   #取值范围1-100
#         preference:
#           matchExpressions:
#             - key: kube
#               operator: In
#               values:
#                 - test

#**********************************************************#
#                      == mount ==                      #
#**********************************************************#

mount:
  volumeMounts: []
  volumes: []

## ========== mount配置示例 ========== #####
# mount:
#   volumeMounts:
#     - name: spring-app-config
#       mountPath: /opt/deployments/config
#       readOnly: true
#     - name: data-dir
#       mountPath: /data
#   volumes:
#     - name: spring-app-config
#       configMap:
#         name: config
#         items:
#         - key: application.yml
#           path: application.yml
#     - name: data-dir
#       persistentVolumeClaim:
#         claimName: datadir-pvc



#**********************************************************#
#                      == Service ==                       #
#**********************************************************#

service:
  enabled: true
  type: NodePort
  ports:
    - name: server
      port: 8080
      targetPort: 8080
      protocol: TCP

#**********************************************************#
#                      == Ingress ==                       #
#**********************************************************#

ingress:
  enabled: false
