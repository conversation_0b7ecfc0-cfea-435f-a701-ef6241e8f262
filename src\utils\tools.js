function download(content, filename) {
    console.log('下载文件', content);

    const eleLink = document.createElement('a');
    eleLink.download = filename;
    eleLink.style.display = 'none';
    // 字符内容转变成blob地址
    const blob = new Blob([content], { type: 'text/csv,charset=UTF-8' });
    eleLink.href = URL.createObjectURL(blob);
    // 触发点击
    document.body.appendChild(eleLink);
    eleLink.click();
    // 然后移除
    document.body.removeChild(eleLink);
};
const chartsTint = {
    bgColor: "#eaeff5",
    leftbgColor: "#3fcbff",
    subtextColor: "#2D3755",
    legendTextColor: "#2D3755",
    xLabelColor: "#2E3641",
    ySplitLineColor: "#ebeced",
    yAxisLabelColor: "#2E3641",
    yAxisLineColor: "#C8C9CB",
    yAxisNameColr: "#798FA7",
    titleTextColor: "#2E3641",
    bgColor: 'rgba(235, 236, 237,0.3)'
}
const chartsDark = {
    bgColor: "#2b3d70",
    leftbgColor: "#3fcbff",
    subtextColor: "#FEFEFF",
    legendTextColor: "#CCE4FF",
    xLabelColor: "#B3D3E5",
    // ySplitLineColor: "#ebeced",
    ySplitLineColor: "rgba(255,255,255,0.1)",
    yAxisLabelColor: "#ACC2E2",
    yAxisLineColor: "#406CA2",
    yAxisNameColr: "#6493D4",
    titleTextColor: "#fff",
    bgColor: 'rgba(27, 34, 66,1)'
}
export { download, chartsTint, chartsDark }