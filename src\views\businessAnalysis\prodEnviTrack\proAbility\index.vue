<template>
  <div class="proAbility">
    <ChartCard :title="'崖城13-1'" class="itemCard">
      <ItemConent></ItemConent>
    </ChartCard>
    <ChartCard :title="'崖城13-10'" class="itemCard">
      <ItemConent></ItemConent>
    </ChartCard>
    <ChartCard :title="'陵水25-1'" class="itemCard">
      <ItemConent></ItemConent>
    </ChartCard>
    <ChartCard :title="'陵水17-2'" class="itemCard">
      <ItemConent></ItemConent>
    </ChartCard>
    <ChartCard :title="'文昌16-2'" class="itemCard">
      <ItemConent></ItemConent>
    </ChartCard>
  </div>
</template>
<script>
import ItemConent from "./ItemConent.vue";
export default {
  name: "proAbility",
  components: { ItemConent },
  data() {
    return {};
  },
};
</script>
<style lang="scss" scoped>
.proAbility {
  display: flex;
  padding: 3% 0;
  .itemCard {
    height: 100%;
    flex: 1;
    margin-left: 12px;
  }
}
</style>
