<template>
  <div id="chart-card">
    <div class="left-top box"></div>
    <div class="right-top box"></div>
    <div class="left-bottom box"></div>
    <div class="right-bottom box"></div>
    <div id="title">
      <div class="title-img"></div>
      <span class="title-text">{{title}}</span>
    </div>
    <slot></slot>
  </div>
</template>
<script>
export default {
  props: {
    title: {
      type: String,
      default: "",
    },
  },
  data() {
    return {};
  },
  mounted() {},
  computed: {},
  methods: {},
};
</script>
<style lang='scss' scoped>
#chart-card {
  position: relative;
  // width: 100%;
  min-height: 242px;
  border: 1px solid #327dff;

  .box {
    position: absolute;
    background-color: #327dff;
  }

  .left-top {
    left: 0;
    top: 0;
    width: 10px;
    height: 10px;
    clip-path: polygon(100% 0%, 0% 0%, 0% 100%);
    /*左上*/
  }

  .right-top {
    right: 0;
    top: 0;
    width: 10px;
    height: 10px;
    clip-path: polygon(100% 0%, 0% 0%, 100% 100%);
    /*右上*/
  }

  .left-bottom {
    left: 0;
    bottom: 0;
    width: 10px;
    height: 10px;
    clip-path: polygon(100% 100%, 0% 0%, 0% 100%);
    /*左下*/
  }

  .right-bottom {
    right: 0;
    bottom: 0;
    width: 10px;
    height: 10px;
    clip-path: polygon(100% 0%, 100% 100%, 0% 100%);
    /*右下*/
  }
}

[data-theme="tint"] #chart-card {
  width: 100%;
  height: 100%;
  background: #ecf7ff;
  box-sizing: border-box;
}

#title {
  height: 43px;
  padding-left:12px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  .title-img {
      margin-right: 8px;
      height: 18px;
      width: 18px;
      background-image: url('@/assets/tableicon/cardleft-icon.png');
      background-size: 100% 100%;
  }
}

[data-theme="tint"] #title {
     color: rgba(0, 0, 0, 0.85);;
  background-image: url("@/assets/tableicon/cardleft-tintbg.png");
}

[data-theme="dark"] #title {
  background-image: url("@/assets/tableicon/cardleft-darkbg.png");
}

[data-theme="dark"] #chart-card {
  width: 100%;
  height: 100%;
  background: #1b2242;
  box-sizing: border-box;
  color: #fff;
}
</style>
