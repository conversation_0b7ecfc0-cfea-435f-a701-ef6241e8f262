<template>
  <div class="word-preview">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      <p>正在加载文档...</p>
    </div>

    <!-- 使用 Office Online 预览 -->
    <iframe
      v-else-if="useOfficeOnline"
      :src="officeOnlineSrc"
      frameborder="0"
      width="100%"
      height="600px"
      class="word-iframe"
      @load="onIframeLoad"
      @error="onIframeError"
    ></iframe>

    <!-- 使用 mammoth.js 渲染 DOCX -->
    <div
      v-else-if="fileType === 'docx' && docxHtml"
      v-html="docxHtml"
      class="docx-container"
    ></div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error">
      <p>{{ error }}</p>
      <div class="error-actions">
        <el-button @click="retry" type="primary" size="small">重试</el-button>
        <el-button @click="tryAlternativePreview" type="default" size="small">尝试其他预览方式</el-button>
        <el-button @click="downloadFile" type="success" size="small">下载文件</el-button>
      </div>
    </div>

    <!-- 不支持的格式 -->
    <div v-else class="unsupported">
      <p>暂不支持该Word文档格式的预览</p>
      <p>支持格式：.docx（本地渲染）、.doc（在线预览）</p>
    </div>
  </div>
</template>

<script>
import mammoth from 'mammoth'

export default {
  name: 'WordPreview',
  props: { 
    fileUrl: {
      type: String,
      required: true,
    },
    fileType: {
      type: String,
      default: 'docx',
    },
    mode: {
      type: String,
      default: 'auto',
    },
  },
  data() {
    return {
      docxHtml: '',
      useOfficeOnline: false,
      officeOnlineSrc: '',
      loading: false,
      error: null,
      currentPreviewMethod: 'office', // office, google, download
    };
  },
  mounted() {
    this.initPreview();
  },
  watch: {
    fileUrl() {
      this.initPreview();
    },
    fileType() {
      this.initPreview();
    },
  },
  methods: {
    initPreview() {
      this.loading = true;
      this.error = null;
      this.currentPreviewMethod = 'office';
      this.setupPreview();
    },

    setupPreview() {
      if (this.fileType === 'docx' && this.mode !== 'online') {
        // DOCX使用本地渲染
        this.useOfficeOnline = false;
        this.renderDocx();
      } else if (this.fileType === 'doc') {
        // DOC文件尝试本地处理或直接提示下载
        this.handleDocFile();
      } else {
        // 其他格式尝试在线预览
        this.useOfficePreview();
      }
    },

    handleDocFile() {
      // DOC格式在内网环境下暂不支持预览
      this.error = 'DOC格式文件暂不支持在线预览，请下载后使用Office软件查看';
      this.loading = false;
    },

    useOfficePreview() {
      this.useOfficeOnline = true;
      this.currentPreviewMethod = 'office';
      // 使用英文环境，避免中文语言包404
      this.officeOnlineSrc = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(this.fileUrl)}&wdStartOn=1&wdEmbedCode=0&wdPrint=0&wdDownloadButton=1&wdNoBorder=1&ui=en-US`;
      this.loading = false;
    },

    useGooglePreview() {
      this.useOfficeOnline = true;
      this.currentPreviewMethod = 'google';
      this.officeOnlineSrc = `https://docs.google.com/gview?url=${encodeURIComponent(this.fileUrl)}&embedded=true`;
      this.loading = false;
    },

    async renderDocx() {
      try {
        const response = await fetch(this.fileUrl);
        if (!response.ok) {
          throw new Error('文件加载失败');
        }
        
        const arrayBuffer = await response.arrayBuffer();
        const result = await mammoth.convertToHtml({ arrayBuffer });
        this.docxHtml = result.value;
        this.loading = false;

        if (result.messages.length > 0) {
          console.warn('Word转换警告:', result.messages);
        }
      } catch (error) {
        console.error('Word文档渲染失败:', error);
        this.fallbackToOnline();
      }
    },

    fallbackToOnline() {
      if (this.currentPreviewMethod === 'office') {
        this.useGooglePreview();
      } else if (this.currentPreviewMethod === 'google') {
        this.useOfficePreview();
      } else {
        this.error = '文档预览失败，建议下载文件查看';
        this.loading = false;
      }
    },

    onIframeLoad() {
      this.loading = false;
      // 延迟检查iframe内容，检测是否为错误页面
      setTimeout(() => {
        this.checkIframeContent();
      }, 3000);
    },

    checkIframeContent() {
      try {
        const iframe = this.$el.querySelector('.word-iframe');
        if (iframe && iframe.contentDocument) {
          const title = iframe.contentDocument.title;
          const body = iframe.contentDocument.body;
          
          // 检查是否为错误页面
          if (title && (title.includes('error') || title.includes('Error'))) {
            this.onIframeError();
            return;
          }
          
          // 检查页面内容是否包含错误信息
          if (body && body.textContent) {
            const content = body.textContent.toLowerCase();
            if (content.includes('error occurred') || 
                content.includes('can\'t process') ||
                content.includes('file not found') ||
                content.includes('access denied')) {
              this.onIframeError();
              return;
            }
          }
        }
      } catch (e) {
        // 跨域限制，无法检查iframe内容
        console.warn('无法检查iframe内容，可能存在跨域限制');
      }
    },

    onIframeError() {
      console.warn(`${this.currentPreviewMethod}预览失败，尝试其他预览方式`);
      if (this.currentPreviewMethod === 'office') {
        this.useGooglePreview();
      } else if (this.currentPreviewMethod === 'google') {
        this.error = '在线预览服务不可用，建议下载文件查看';
        this.loading = false;
      }
    },

    retry() {
      this.initPreview();
    },

    tryAlternativePreview() {
      if (this.currentPreviewMethod === 'office') {
        this.useGooglePreview();
      } else {
        this.useOfficePreview();
      }
    },

    downloadFile() {
      const link = document.createElement('a');
      link.href = this.fileUrl;
      link.download = this.fileUrl.split('/').pop() || 'document';
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
  },
};
</script>

<style scoped>
.word-preview {
  width: 100%;
  height: 100%;
  border: 1px solid #ddd;
  position: relative;
}

.loading, .error, .unsupported {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400px;
  color: #666;
  text-align: center;
}

.error-actions {
  margin-top: 16px;
}

.error-actions .el-button {
  margin: 0 4px;
}

.word-iframe {
  border: none;
}

.docx-container {
  padding: 20px;
  background: #fff;
  height: 600px;
  overflow-y: auto;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  line-height: 1.6;
}

.docx-container :deep(p) {
  margin: 8px 0;
}

.docx-container :deep(h1),
.docx-container :deep(h2),
.docx-container :deep(h3) {
  margin: 16px 0 8px 0;
  font-weight: bold;
}

.docx-container :deep(table) {
  border-collapse: collapse;
  width: 100%;
  margin: 16px 0;
}

.docx-container :deep(td),
.docx-container :deep(th) {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}
</style>
