<template>
  <div class="date-picker" :data-date-type="actualDateType">
    <p class="picker-text">年初至</p>
    <el-date-picker
      v-model="selectedDate"
      :type="actualDateType"
      :placeholder="placeholder"
      :clearable="false"
      @change="handleDateChange"
      :value-format="valueFormat"
      :format="displayFormat"
    >
    </el-date-picker>
  </div>
</template>

<script>
export default {
  name: "DatePicker",
  props: {
    value: {
      type: [String, Date],
      default: "",
    },
    // 新的dateType属性，支持可配置的精度级别
    dateType: {
      type: String,
      default: "month", // 默认为月份选择，保持向后兼容
      validator: (value) => ["month", "date", "day"].includes(value)
    },
    // 保持type属性的向后兼容性
    type: {
      type: String,
      default: "",
      validator: (value) => value === "" || ["month", "date"].includes(value)
    },
  },
  computed: {
    // 计算实际使用的日期类型，优先使用dateType，其次使用type（向后兼容）
    actualDateType() {
      // 如果显式设置了type属性，使用type（向后兼容）
      if (this.type) {
        return this.type;
      }
      // 否则使用dateType，支持"day"作为"date"的别名
      return this.dateType === "day" ? "date" : this.dateType;
    },
    placeholder() {
      return this.actualDateType === "month" ? "选择月" : "选择日期";
    },
    valueFormat() {
      return this.actualDateType === "month" ? "yyyy-MM" : "yyyy-MM-dd";
    },
    displayFormat() {
      return this.actualDateType === "month" ? "yyyy-MM" : "yyyy-MM-dd";
    }
  },
  data() {
    return {
      selectedDate: this.value,
    };
  },
  watch: {
    // 监听props的变化，保持与父组件同步
    value(newVal) {
      this.selectedDate = newVal;
    },
  },
  methods: {
    handleDateChange(val) {
      // 向父组件发射事件，传递选中的日期
      this.$emit("change", val);
      // 如果需要支持v-model双向绑定
      this.$emit("input", val);
    },
  },
};
</script>

<style lang="scss" scoped>
.date-picker {
  display: flex;
  align-items: center;
  border-radius: 6px;
  background: #263e78;
  .picker-text {
    padding-left: 12px;
    font-family: Source Han Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    letter-spacing: normal;
    display: flex;
    align-items: center;
  }
}

.picker-text::after {
  display: block;
  content: "";
  width: 1px;
  height: 18px;
  background: rgba(174, 189, 218, 0.2);
  margin-left: 12px;
  line-height: 22px;
}

::v-deep .el-date-editor.el-input {
  width: 120px;
}

/* 根据日期类型调整宽度 */
.date-picker[data-date-type="date"] ::v-deep .el-date-editor.el-input {
  width: 140px;
}
::v-deep .el-input__inner {
  padding: 0;
  height: 32px;
  line-height: 32px;

  padding-left: 15px;
  border: none;

  font-family: Source Han Sans;
  font-size: 14px;
  font-weight: normal;
}
::v-deep .el-input__prefix {
  left: 78%;
  .el-input__icon {
    line-height: 32px;
  }
}
</style>

<style scoped>
[data-theme="dark"] .picker-text {
  color: #aebdda;
}
[data-theme="dark"] .date-picker /deep/ .el-input__inner {
  background-color: #263e78;
  color: #6ba4f4;
}

[data-theme="tint"] .picker-text {
  color: #5e8be1;
}
[data-theme="tint"] .date-picker /deep/ .el-input__inner {
  background: #ffffff;
  color: rgba(0, 0, 0, 0.65);
}
</style>
