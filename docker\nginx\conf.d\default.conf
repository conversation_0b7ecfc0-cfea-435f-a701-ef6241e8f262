server {
    listen       8080;
    listen  [::]:8080;
    server_name  localhost;
    underscores_in_headers on;
    client_max_body_size 10m;
    location / {
	    add_header X-Frame-Options SAMEORIGIN;
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        try_files $uri $uri/ /fbpApp/index.html;
    }

    location /api/ {
        rewrite ^/api/(.*) /$1 break;
        proxy_pass http://*************:31081;
        proxy_connect_timeout 20;
        proxy_read_timeout 20;
    }

    location /static-api/ {
        rewrite ^/static-api/(.*) /$1 break;
        proxy_pass http://*************:31999;
        proxy_connect_timeout 6;
        proxy_read_timeout 6;
    }

    location /lowcode-websocket/ {
        proxy_pass http://*************:30666;
        proxy_read_timeout 600;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        #proxy_set_header Connection $connection_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Origin "";
        rewrite ^/lowcode-websocket/(.*) /$1 break;
    }




    location /ver/ {
        rewrite ^/ver/(.*) /$1 break;
        rewrite ^/asisstant(.*) /api$1 break;
        proxy_pass http://*************:31081;
        proxy_connect_timeout 6;
        proxy_read_timeout 6;
    }

    location /asisstant/ {
        rewrite ^/asisstant/(.*) /api/$1 break;
        proxy_pass http://*************:31902;
        proxy_connect_timeout 6;
        proxy_read_timeout 6;
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

}
