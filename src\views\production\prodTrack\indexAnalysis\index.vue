<template>
  <div class="index-analysis">
    <div class="content-box">
      <div class="left-box">
        <div class="search-box">
          <OgwSearch
            :fields="searchFields"
            v-model="searchForm"
            @search="handleSearch"
          >
          </OgwSearch>
          <el-button style="margin-left: -10%;" type="primary" @click="handleExit">返回</el-button>
        </div>
        <div class="table-container">
          <OgwTable
            :columns="columns"
            :data="tableData"
            :page-size="10"
            :current-page="1"
          >
          </OgwTable>
        </div>
      </div>
      <div class="right-box">
        <div>
          <div class="title">
            <div class="line"></div>
            同比增长率
          </div>
          <div class="chart-box">
            <YearOnyear></YearOnyear>
          </div>
        </div>
        <div>
          <div class="title">
            <div class="line"></div>
            环比增长率
          </div>
          <div class="chart-box">
            <GrowthRate></GrowthRate>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import OgwSearch from "@/components/comTable/OgwSearch.vue";
import OgwTable from "@/components/comTable/OgwTable.vue";
import YearOnyear from "./yearOnyear.vue";
import GrowthRate from "./growthRate.vue";
export default {
  name: "indexAnalysis",
  components: {
    OgwSearch,
    OgwTable,
    YearOnyear,
    GrowthRate,
  },
  mounted() {
    window.document.documentElement.setAttribute("data-theme", "tint");
  },
  data() {
    return {
      searchForm: {
        planYear: "",
        deviceNameCode: "",
        monthRange: ["", ""],
      },
      searchFields: [
        {
          label: "计划年度:",
          prop: "planYear",
          type: "year",
        },
        {
          label: "平台名称:",
          prop: "deviceNameCode",
          type: "select",
          options: [
            { label: "全部", value: "all" },
            { label: "深海一号", value: "sh1" },
            { label: "崖城13-1", value: "yc13" },
          ],
        },
      ],
      columns: [
        { label: "时间", prop: "time" },
        { label: "作业平台", prop: "platform" },
        { label: "当期产量", prop: "currentProd" },
        { label: "累计产量", prop: "totalProd" },
        { label: "环比增长率(%)", prop: "growthRate" },
        { label: "上年同期产量", prop: "lastYearProd" },
        { label: "同比增长率(%)", prop: "yearOnYearRate" },
        { label: "上年累计产量", prop: "lastYearTotalProd" },
      ],
      tableData: [
        {
          planYear: "2022",
          deviceNameType: "深海一号",
          productType: "原油",
          time: "2022-01",
          platform: "平台1",
          currentProd: "100",
          totalProd: "1000",
          growthRate: "10",
          lastYearProd: "90",
          yearOnYearRate: "10",
          lastYearTotalProd: "900",
        },
      ],
    };
  },
  methods: {
    handleSearch() {
      console.log(this.searchForm);
    },
    handleExit() {
      this.$router.back();
    },
  },
};
</script>
<style lang="scss" scoped>
.index-analysis {
  .content-box {
    display: flex;
    .left-box {
      flex: 7;
      max-width: 65%;
      box-sizing: border-box;
      padding-left: 10px;
      .search-box {
        display: flex;
        align-items: center;
      }
    }
    .right-box {
      flex: 3;
      margin: 20px 0;
      font-family: Source Han Sans;
      font-size: 14px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);

      .line {
        display: inline-block;
        width: 4px;
        height: 12px;
        margin-right: 8px;
        background: #1677ff;
      }
    }
  }
}
</style>
