<template>
  <div class="ogw-container">
    <div class="ogw-container-top">
      <div class="budget">
        <chartBox :title="'预算执行'">
          <template v-slot:box-right>
            <DatePicker
              class="date-picker"
              v-model="selectedMonth"
              @change="handleMonthChange(type='budget')"
            />
          </template>
          <BudgetBox :date="selectedMonth"></BudgetBox>
        </chartBox>
      </div>
      <div class="budget medicine">
        <chartBox :title="'方油/方气处理费'">
          <template v-slot:box-right>
            <DatePicker class="date-picker" v-model="selectedMonth2" @change="handleMonthChange(type='oil')"/>
          </template>
          <ProcessFeeBox :date="selectedMonth2"></ProcessFeeBox>
        </chartBox>
      </div>
      <div class="budget">
        <chartBox :title="'单位药剂处理气量/液量'">
          <UnitDosage></UnitDosage>
        </chartBox>
      </div>
    </div>
    <div class="ogw-container-bottom">
      <div class="medicine">
        <chartBox :title="'年度药剂加注'">
          <template v-slot:box-right>
            <div class="info-btn" @click="toUseMechinePage">详情</div>
            <DatePicker class="date-picker" type="date" v-model="selectedMonth3" @change="handleMonthChange(type='medicine')"/>
          </template>
          <DrugAddition :date="selectedMonth3"></DrugAddition>
        </chartBox>
      </div>
      <div class="medicine service">
        <chartBox :title="'药剂服务/分析化验'">
          <template v-slot:box-right>
            <DatePicker class="date-picker" v-model="selectedMonth4" @change="handleMonthChange(type='service')"/>
          </template>
          <MedicineService :date="selectedMonth4"></MedicineService>
        </chartBox>
      </div>
    </div>
  </div>
</template>
<script>
import BudgetBox from "./budgetExecution/index.vue";
import ProcessFeeBox from "./processFeeBox/index.vue";
import UnitDosage from "./unitDosage/index.vue";
import DrugAddition from "./drugAddition/index.vue";
import MedicineService from "./medicineService/index.vue";
import DatePicker from "./DatePicker.vue";
import { getPreMonth,getDay } from "@/utils/dateTools";
export default {
  name: "ogw",
  components: {
    BudgetBox,
    ProcessFeeBox,
    UnitDosage,
    DrugAddition,
    MedicineService,
    DatePicker,
  },
  data() {
    return {
      selectedMonth: getPreMonth(),
      selectedMonth2: getPreMonth(),
      selectedMonth3: getDay(),
      selectedMonth4: getPreMonth(),
    };
  },
  methods: {
    toUseMechinePage() {
      this.$router.push("/ogw/ogwActiveRecord/useChemical");
    },
    handleMonthChange(type) {
      switch (type) {
        case "budget":
          this.selectedMonth = this.selectedMonth;
          break;
        case "oil":
          this.selectedMonth2 = this.selectedMonth2;
          break;
        case "medicine":
          this.selectedMonth3 = this.selectedMonth3;
          break;
        case "service":
          this.selectedMonth4 = this.selectedMonth4;
          break;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.ogw-container {
  .ogw-container-top {
    display: flex;
    margin-bottom: 20px;
    .budget {
      flex: 1;
    }
    .medicine {
      margin: 0 10px;
    }
  }
  .ogw-container-bottom {
    display: flex;
    min-width: 0;
    .medicine {
      flex: 1;
    }
    .service {
      margin-left: 16px;
    }
  }
}

.date-picker {
  margin-right: 12px;
}

.info-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 72px;
  height: 32px;
  font-family: Source Han Sans;
  font-size: 14px;
  font-weight: normal;
  background-size: 100% 100%;
  margin-right: 16px;
  cursor: pointer;
}

[data-theme="dark"] .info-btn {
  background-image: url("@/assets/tableicon/info-darkbtn.png");
  color: #ffffff;
}
</style>
