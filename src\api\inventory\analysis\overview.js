import { instance } from "../../config";



//库存总览-一年以上库存
export function countOneGtAmount(data) {
    return instance({
        url: '/im-ls/nventoryOverview/countOneGtAmount',
        method: "POST",
        data
    });
}
//库存总览-当年收货未发货
export function countRecAndNotShippedAmount(data) {
    return instance({
        url: '/im-ls/nventoryOverview/countRecAndNotShippedAmount',
        method: "POST",
        data
    });
}
//库存总览-安全库存
export function countSafetyAmount(data) {
    return instance({
        url: '/im-ls/nventoryOverview/countSafetyAmount',
        method: "POST",
        data
    });
}
//库存总览-总库存
export function inventoryOverviewTrend(data) {
    return instance({
        url: '/im-ls/nventoryOverview/inventoryOverviewTrend',
        method: "POST",
        data
    });
}
//库存总览-在途PO
export function countWalkPoAmount(data) {
    return instance({
        url: '/im-ls/nventoryOverview/countWalkPoAmount',
        method: "POST",
        data
    });
}
//总库存列表导出
export function exportFile(data) {
    return instance({
        url: '/im-ls/nventoryOverview/export',
        method: "POST",
        responseType: 'blob',
        data
    });
}


// 采购消耗比
//采购消耗比-出入库对比统计
export function countInAndOut(params) {
    return instance({
        url: '/im-ls/sapInfoReceiveDelivery/countInAndOut',
        method: "GET",
        params
    });
}
//采购消耗比-采购周转率
export function countTurnoverRate(params) {
    return instance({
        url: '/im-ls/sapInfoReceiveDelivery/countTurnoverRate',
        method: "GET",
        params
    });
}
//采购消耗比-统计出入库信息列表
export function countReceiveDeliveryInfos(data) {
    return instance({
        url: '/im-ls/sapInfoReceiveDelivery/countReceiveDeliveryInfos',
        method: "POST",
        data
    });
}
//采购消耗比-统计收发库信息列表导出
export function export2(data) {
    return instance({
        url: '/im-ls/sapInfoReceiveDelivery/export',
        method: "POST",
        responseType: 'blob',
        data
    });
}

// 零价值物资库
//零价值物资列表导出
export function export3(data) {
    return instance({
        url: '/im-ls/worthlessMaterial/export',
        method: "POST",
        responseType: 'blob',
        data
    });
}
//零价值物资列表查询
export function list(data) {
    return instance({
        url: '/im-ls/worthlessMaterial/list',
        method: "POST",
        data
    });
}
//统计陵水和崖城库存总量
export function countInventory(params) {
    return instance({
        url: '/im-ls/worthlessMaterial/countInventory',
        method: "GET",
        params
    });
}
// 在途PO
//在途PO库存统计
export function countPoAmount(params) {
    return instance({
        url: '/im-ls/purchaseOrder/countPoAmount',
        method: "GET",
        params
    });
}
//零价值物资列表导出
export function export4(data) {
    return instance({
        url: '/im-ls/purchaseOrder/export',
        method: "POST",
        responseType: 'blob',
        data
    });
}
//在途PO列表查询
export function list2(data) {
    return instance({
        url: '/im-ls/purchaseOrder/list',
        method: "POST",
        data
    });
}


// 库存管理周报
//库存管理周报列表查询
export function weeklyList(data) {
    return instance({
        url: '/im-ls/sapWeekReport/list',
        method: "POST",
        data
    });
}
//邮件配置保存
export function weeklySave(data) {
    return instance({
        url: '/im-ls/sapWeekReport/save',
        method: "POST",
        data
    });
}
//邮件配置编辑
export function editEmailConfig(params) {
    return instance({
        url: '/im-ls/sapWeekReport/editEmailConfig',
        method: "GET",
        params
    });
}
//采购消耗比-获取出入库移动类型数据
export function getReceiveDeliveryBwart(params) {
    return instance({
        url: '/im-ls/sapInfoReceiveDelivery/getReceiveDeliveryBwart',
        method: "GET",
        params
    });
}
//采购消耗比-获取出入库信息列表数据导出
export function exportReceiveDeliveryInfos(data) {
    return instance({
        url: '/im-ls/sapInfoReceiveDelivery/exportReceiveDeliveryInfos',
        method: "POST",
        responseType: 'blob',
        data
    });
}
//采购消耗比-获取出入库信息列表数据
export function getReceiveDeliveryInfos(data) {
    return instance({
        url: '/im-ls/sapInfoReceiveDelivery/getReceiveDeliveryInfos',
        method: "POST",
        data
    });
}

