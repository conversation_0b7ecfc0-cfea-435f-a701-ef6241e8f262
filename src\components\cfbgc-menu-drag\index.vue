<template>
    <div class="cfbgc-menu-drag">
        <div ref="dragDom" title="可拖拽挪动">
            <cf-icon type="menu-unfold" @click="changeMenuOpen(true)" v-show="!menuOpen"/>
            <cf-icon type="menu-fold" @click="changeMenuOpen(false)" v-show="menuOpen"/>
        </div>
    </div>
</template>
<script>
export default {
    name: 'cfbgcMenuDrag',
    data() {
        return {}
    },
    props: {
        menuOpen: {
            type: Boolean,
            default: false
        }
    },
    mounted() {
        this.$nextTick()
            .then(() => {
                this.dragInit()
            })
    },
    methods: {
        changeMenuOpen(bo) {
            this.$emit('change', bo)
        },
        dragInit() {
            const dom = this.$refs.dragDom
            dom.onmousedown = function (ev) {
                ev.preventDefault()
                ev.stopPropagation()
                var oevent = ev || event;

                var distanceX = oevent.clientX - dom.offsetLeft;
                var distanceY = oevent.clientY - dom.offsetTop;

                document.onmousemove = function (ev) {
                    var oevent = ev || event;
                    dom.style.left = oevent.clientX - distanceX + 'px';
                    dom.style.top = oevent.clientY - distanceY + 'px';
                };
                document.onmouseup = function (eo) {
                    eo.preventDefault()
                    eo.stopPropagation()
                    document.onmousemove = null;
                    document.onmouseup = null;
                };
            };
        }
    }
}
</script>
<style lang="scss">
.cfbgc-menu-drag {
    position: absolute;
    left: 3rem;
    bottom: 5rem;
    z-index: 7;
    font-size: 2rem;
    box-shadow: 0 0 5px #52C41A;
    cursor: pointer;

    & > div {
        position: absolute;

        & > i {
            box-shadow: 0 0 5px #1890ff;
        }
    }

}
</style>
