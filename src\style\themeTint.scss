[data-theme="defaule"],
[data-theme="tint"] {
    $font_color: #2E3641;
    $icon_color: #3a495a;
    $border_color: #EAEFF5;


    $table_bg1: #EAEFF5;
    $header_font_color: #97A0AA;
    $input_bg: #b8cadd;

    .cfbgc-input {
        background-color: $input_bg;
        color: $font_color;
    }

    .cfbgc-radio-group {
        .cfbgc-radio-button-wrapper {
            background-color: #fff;
            color: $font_color;
            border-color: #4EA0FD;
        }

        .cfbgc-radio-button-wrapper-checked {
            background-color: #409eff;
            color: #fff;
        }
    }

    .cfbgc-calendar-picker {

        .cfbgc-input {
            background-color: $input_bg;
            color: $font_color;
        }

        .cfbgc-calendar-picker-clear {
            background-color: $input_bg;
            color: $icon_color;
        }

        .cfbgcicon svg {
            color: $icon_color;
        }
    }

    // 日期选择器弹出框
    .cfbgc-calendar {

        // 弹出框头部input
        .cfbgc-calendar-input-wrap {
            background: #fff;
            border-bottom: 1px solid $border_color;

            .cfbgc-calendar-input {
                background: #fff;
                color: $font_color;
            }
        }

        // 日期选择主体
        .cfbgc-calendar-month-panel,
        .cfbgc-calendar-date-panel {

            // 头部
            .cfbgc-calendar-year-panel-header,
            .cfbgc-calendar-month-panel-header,
            .cfbgc-calendar-header {
                border-bottom: 2px solid $border_color;
                background-color: #fff;

                .cfbgc-calendar-month-panel-year-select {
                    color: $font_color;
                }

                .cfbgc-calendar-year-select {
                    color: $font_color;
                }

                .cfbgc-calendar-month-select {
                    color: $font_color;
                }

                .cfbgc-calendar-year-panel-decade-select-content {
                    color: $font_color;
                }

                // >>,> 的颜色
                .cfbgc-calendar-month-panel-prev-year-btn::after,
                .cfbgc-calendar-month-panel-prev-year-btn::before,
                .cfbgc-calendar-month-panel-next-year-btn::after,
                .cfbgc-calendar-month-panel-next-year-btn::before,
                .cfbgc-calendar-year-panel-prev-decade-btn::after,
                .cfbgc-calendar-year-panel-prev-decade-btn::before,
                .cfbgc-calendar-year-panel-next-decade-btn::before,
                .cfbgc-calendar-year-panel-next-decade-btn::before,
                .cfbgc-calendar-next-month-btn::before,
                .cfbgc-calendar-next-year-btn::before {
                    border-color: #8597CD;
                }
            }

            // 中间
            .cfbgc-calendar-year-panel-body,
            .cfbgc-calendar-month-panel-body,
            .cfbgc-calendar-body {
                border-bottom: 2px solid $border_color;
                background-color: #fff;

                .cfbgc-calendar-month-panel-table,
                .cfbgc-calendar-year-panel-table,
                .cfbgc-calendar-table {
                    .cfbgc-calendar-column-header {
                        color: $font_color;
                    }

                    .cfbgc-calendar-month-panel-month,
                    .cfbgc-calendar-year-panel-year,
                    .cfbgc-calendar-date {
                        color: $font_color;
                    }

                    .cfbgc-calendar-month-panel-month:hover,
                    .cfbgc-calendar-year-panel-year:hover,
                    .cfbgc-calendar-date:hover {
                        color: #fefefe;
                    }

                    .cfbgc-calendar-month-panel-selected-cell .cfbgc-calendar-month-panel-month,
                    .cfbgc-calendar-year-panel-selected-cell .cfbgc-calendar-year-panel-year,
                    .cfbgc-calendar-selected-date .cfbgc-calendar-date {
                        color: #fefefe;
                    }

                    .cfbgc-calendar-month-panel-cell-disabled .cfbgc-calendar-month-panel-month,
                    .cfbgc-calendar-year-panel-cell-disabled .cfbgc-calendar-year-panel-year,
                    .cfbgc-calendar-disabled-cell .cfbgc-calendar-date {
                        background-color: #eaeff5;
                        color: $font_color;
                    }
                }
            }

            // 底部
            .cfbgc-calendar-footer {
                background-color: #fff;

                .cfbgc-calendar-today-btn,
                .cfbgc-calendar-today-btn-disabled {
                    color: $font_color;
                }
            }
        }
    }

    // 年份选择器弹出框
    // 表格
    // 空数据时
    .cfbgc-table-empty .cfbgc-table-placeholder {
        background-color: $table_bg1;

        .cfbgc-empty-description {
            color: #97A0AA;
        }

        g {
            stroke: #97A0AA;
            fill: #97A0AA;
        }
    }

    .cfbgc-table-pagination .cfbgc-pagination-options-quick-jumper input {
        background-color: $input_bg;
        color: $font_color;
    }

    // 正常样式
    .cfbgc-table {


        .cfbgc-table-thead {
            border: 1px solid #d8dee6;
            border-radius: 10px 10px 0px 0px;
        }

        .cfbgc-table-content .cfbgc-table-thead th {
            background-color: #fff;
            color: $header_font_color;
        }

        .cfbgc-table-body::-webkit-scrollbar-track {
            background: #d2d9e0;
            /* 滚动条轨道颜色 */
        }

        .cfbgc-table-body::-webkit-scrollbar-thumb {
            background: #b5bec6;
            /* 滚动条滑块颜色 */
        }

        .cfbgc-table-content .cfbgc-table-body,
        .cfbgc-table-scroll .cfbgc-table-body {
            background-color: #ffffff !important;

            .cfbgc-table-row {
                background-color: #fff;
            }

            .cfbgc-table-row:nth-of-type(odd) td,
            .cfbgc-table-row:nth-of-type(odd) td {
                background-color: $table_bg1;
                color: $font_color;
            }

            .cfbgc-table-row:nth-of-type(even) td,
            .cfbgc-table-row:nth-of-type(even) td {
                background-color: #fff;
                color: $font_color;
            }

            tr:hover:not(.cfbgc-table-expanded-row):not(.cfbgc-table-row-selected) td {
                background-color: #d7ecf7;
            }
        }


    }

    .cfbgc-table-pagination .cfbgc-pagination-total-text {
        color: $header_font_color;
    }

    // 表单
    .cfbgc-form-item label {
        color: $font_color;
    }


    // 下拉

    .cfbgc-select-selection--single {
        background-color: $input_bg;
        color: $font_color;

        .cfbgc-select-selection-selected-value {
            color: $font_color;
        }

        .cfbgcicon {
            color: $icon_color;
            background-color: $input_bg;
        }
    }

    .cfbgc-select-selection--single:hover {
        border-color: $border_color;
    }

    .downMenu {
        background-color: #ffffff;

        .cfbgc-select-dropdown-menu-item {
            color: #2e3641;
        }

        .cfbgc-select-dropdown-menu-item-selected {
            background-color: #e4f1ff;
            color: #4ea0fd;
        }

        .cfbgc-select-dropdown-menu-item-active:not(.cfbgc-select-dropdown-menu-item-disabled) {
            background-color: #e4f1ff;
            color: #4ea0fd;
        }

        .cfbgc-select-dropdown-menu-item:hover:not(.cfbgc-select-dropdown-menu-item-disabled) {
            color: #4ea0fd;
            background-color: #e4f1ff;
        }
    }
}