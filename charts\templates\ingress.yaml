{{- if .Values.ingress.enabled }}
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: "{{ .Release.Name }}"
  labels:
    {{- include "spring-boot.labels" . | nindent 4 }}
  annotations:
    {{- if .tls }}
    ingress.kubernetes.io/secure-backends: "true"
    {{- end }}
    {{- range $key, $value := .Values.ingress.annotations }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
spec:
  rules:
  {{- if .Values.ingress.hostname }}
    - host: {{ .Values.ingress.hostname }}
      http:
        paths:
        - path: {{ .Values.ingress.path }}
          backend:
            serviceName: {{ default .Release.Name .Values.ingress.service }}
            servicePort: {{ .Values.ingress.port }}
  {{- end }}
  {{- range .Values.ingress.hosts }}
    - host: {{ .name }}
      http:
        paths:
        - path: {{ default "/" .path }}
          backend:
            serviceName: {{ .service }}
            servicePort: {{ .port }}
  {{- end }}
  {{- if .Values.ingress.tls }}
  tls: 
    {{- toYaml .Values.ingress.tls | nindent 4 }}
  {{- end }}
{{- end }}