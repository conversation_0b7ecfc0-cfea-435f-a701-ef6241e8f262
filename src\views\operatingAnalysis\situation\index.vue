<template>
  <div class="situation">
    <div class="top"></div>
    <div class="content-box">
      <div class="content-left">
        <div>
          <ChartBox :title="'经营趋势'">
            <OperatTrends />
          </ChartBox>
        </div>
        <div class="oil-cost">
          <ChartBox :title="'桶油成本'">
            <OilCost />
          </ChartBox>
        </div>
      </div>
      <div class="content-right">
        <ChartBox :title="'“四提高”情况'">
          <Complexion />
        </ChartBox>
      </div>
    </div>
  </div>
</template>
<script>
import OperatTrends from "./operatTrends/index.vue";
import OilCost from "./oilCost/index.vue";
import Complexion from "./complexion/index.vue";
export default {
  name: "Situation",
  components: {
    OperatTrends,
    OilCost,
    Complexion,
  },
};
</script>
<style lang="scss" scoped>
.situation {
  .content-box {
    height: 100%;
    display: flex;
    .content-left {
      flex: 1;
      margin-right: 10px;
    }
    .content-right {
      flex: 1;
    }
    .oil-cost {
      margin-top: 10px;
    }
  }
}
</style>
