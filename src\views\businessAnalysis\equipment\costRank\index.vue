<template>
  <div class="costRank">
    <common-table
      :tableData="tableData"
      :colums="colums"
      :border="false"
    ></common-table>
  </div>
</template>
<script>
import commonTable from "@/components/comTable/commonTable.vue";
export default {
  name: "costRank",
  components: {
    commonTable,
  },
  data() {
    return {
      colums:[
        {
          label: "所属设备设施",
          prop: "facilities",
        },
        {
          label: "设备名称",
          prop: "name",
        },
        {
          label:"固定资产购置(万元)",
          prop: "assets",
        },
        {
          label: "使用年限(年)",
          prop: "year",
        },
        {
          label: "维修费用(万元)",
          prop: "cost",
        },
        {
          label: "维修次数",
          prop: "times",
        }
      ],
      tableData: [
        {
          facilities: "设备设施1",
          name: "设备1",
          assets: "100",
          year: "10",
          cost: "100",
          times: "10",
        },
        {
          facilities: "设备设施2",
          name: "设备2",
          assets: "100",
          year: "10",
          cost: "100",
          times: "10",
        },
        {
          facilities: "设备设施3",
          name: "设备3",
          assets: "100",
          year: "10",
          cost: "100",
          times: "10",
        },
      ],
    };
  },
};
</script>
<style lang="scss" scoped>
.costRank {
  padding: 16px;
}

</style>
