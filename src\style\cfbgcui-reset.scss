@use "sass:math";

$input-color: #6493d4; // 所有inout框的字体颜色
$input-bg-color: #2d3755; // 所有inout框的背景颜色
$title-color1: #cce4ff; //标题色
$title-color2: #b7ffde; //标题色
// 默认设计稿的宽度
$designWidth: 1920;
// 默认设计稿的高度
$designHeight: 1080;

// px 转为 vw 的函数
@function vw($px) {
  @return math.div($px, $designWidth) * 100vw;
}

// px 转为 vh 的函数
@function vh($px) {
  @return math.div($px, $designHeight) * 100vh;
}
// 表格设置y轴滚动时导致表头被遮挡
.cfbgc-table-hide-scrollbar {
  padding-bottom: 20px !important;
}
// 标签页组件样式
.cfbgc-tabs-bar {
  border: none;
  height: vh(45);
  margin-bottom: vh(16);
}

// 未选中标签页的样式
.cfbgc-tabs-tab {
  font-size: vh(18);
  color: #fff;
  box-sizing: border-box;
}

// 时间选择器
// 时间选择器右侧图标样式
.cfbgc-calendar-picker-icon {
  line-height: inherit;
  color: rgba($color: #e1e4ea, $alpha: 0.6);
}

.cfbgc-calendar-picker div {
  display: flex;
}

.cfbgc-calendar-picker-input {
  font-size: vh(14);
  height: vh(30);
  line-height: vh(20);
}

.cfbgc-calendar-picker-input.cfbgc-input {
  line-height: 1;
}

.cfbgc-input {
  border-radius: vh(7);
  border: none;
  background-color: $input-bg-color;
  color: $input-color;

  // .cfbgc-calendar-range-picker-input,
  .cfbgc-calendar-range-picker-input::placeholder {
    color: $input-color;
  }
}

// 年选择器 禁用样式
.cfbgc-calendar-year-panel-cell-disabled {
  .cfbgc-calendar-year-panel-year {
    background-color: #000;
    color: #fff;
  }
}

// 日期选择器 禁用样式
.cfbgc-calendar-disabled-cell {
  .cfbgc-calendar-date {
    background-color: #000;
    color: #fff;
  }
}

// 月份选择器 禁用样式
.cfbgc-calendar-month-panel-cell-disabled {
  .cfbgc-calendar-month-panel-month {
    background-color: #000;
    color: #fff;
  }
}

// 清除图标的样式
.cfbgc-calendar-picker-clear {
  color: #5d6b88;
  background: $input-bg-color;
}

.cfbgc-calendar-picker-clear:hover {
  color: #97abc7;
  background: $input-bg-color;
}

.cfbgc-input::placeholder {
  color: $input-color;
}

// 表单项样式
.cfbgc-form-item-control {
  line-height: vh(30);
  height: vh(30);
}

.cfbgc-form-item-label {
  height: vh(30);
  line-height: vh(30);
}

.cfbgc-form-item label {
  color: white;
  font-size: vh(14);
  text-align: center;
}

.cfbgc-form-horizontal .cfbgc-form-item {
  display: flex;
}

// 下拉框相关样式
// 下拉框的输入框样式
.cfbgc-select-selection__rendered {
  line-height: vh(30);
  height: vh(30);

  .cfbgc-select-search--inline {
    color: $input-color;
  }
}

.cfbgc-select-selection--multiple {
  min-height: 24px;

  .cfbgc-select-selection__rendered > ul > li {
    height: 22px;
    line-height: 22px;
    margin-top: 1px;
    background-color: #24325d;
    border-color: $input-color;

    .cfbgc-select-selection__choice__content {
      color: $input-color;
    }
  }

  .cfbgc-select-selection__clear {
    top: 12px;
  }
}

.cfbgc-select-selection--single,
.cfbgc-select-selection--multiple {
  background-color: $input-bg-color;
  line-height: vh(30);
  min-height: vh(30);
  min-width: vh(120);
  border: none;
  border-radius: vh(7);

  .cfbgc-select-selection__clear {
    background-color: transparent;
  }

  .cfbgc-select-selection-selected-value {
    color: $input-color;
    line-height: vh(30);
    height: vh(30);
    font-size: vh(14);
  }

  // 清除图标样式
  .cfbgcicon {
    color: #97abc7;
    background: #2f3957;
  }
}

.cfbgc-select-selection--single:hover,
.cfbgc-select-selection--multiple:hover {
  border: 1px solid #1257a1;
  background-color: #1f2a4a;
}

// row col
.cfbgc-row {
  height: vh(30);
  margin-top: vh(5);
}

//  单选框相关
// 单选框的选项样式
.cfbgc-radio-button-wrapper {
  height: vh(30);
  min-width: vh(55);
  font-size: vh(16);
}

.cfbgc-radio-group {
  .cfbgc-radio-button-wrapper {
    background-color: #0c0d15;
    border: 1px solid #2665a8;
    color: white;
    line-height: vh(30);
  }

  .cfbgc-radio-button-wrapper:not(:first-child)::before {
    background-color: #153465;
    display: none !important;
  }

  .cfbgc-radio-button-wrapper:nth-child(1) {
    border-radius: vh(7) 0 0 vh(7);
  }

  .cfbgc-radio-button-wrapper:nth-last-child(1) {
    border-radius: 0 vh(7) vh(7) 0;
  }

  .cfbgc-radio-button-wrapper-checked {
    background-color: #409eff;
  }
}

.cfbgc-radio-button-wrapper-checked:not(
    .cfbgc-radio-button-wrapper-disabled
  ):hover {
  color: #fff;
}

[data-theme="defaule"],
[data-theme="tint"] {
  .cfbgc-radio-group {
    .cfbgc-radio-button-wrapper {
      background-color: #fff;
      color: #2e3641;
      border-color: #4ea0fd;
    }

    .cfbgc-radio-button-wrapper-checked {
      background-color: #409eff;
      color: #fff;
    }
  }
}

// 按钮
.cfbgc-btn-sm {
  font-size: vh(14);
  height: vh(30);
  line-height: vh(30);
  border-radius: vh(7);
}

.cfbgc-btn-primary {
  background-color: #639ef6;
  border-color: #639ef6;
}

.cfbgc-btn-primary:hover {
  background-color: #4a81d8;
  border-color: #4a81d8;
}

// 时间范围选择器的下拉菜单
.cfbgc-calendar {
  background-color: $input-bg-color;
  color: #fff;
  border: none;

  .cfbgc-calendar-header {
    border-bottom: 2px solid #3b466a;

    // <<,< 的颜色
    .cfbgc-calendar-prev-month-btn::before,
    .cfbgc-calendar-prev-year-btn::before {
      border: 1px solid $input-color;
      border-width: 1.5px 0 0 1.5px;
    }

    .cfbgc-calendar-prev-year-btn::after {
      border: 1px solid $input-color;
      border-width: 1.5px 0 0 1.5px;
    }

    // >>,> 的颜色
    .cfbgc-calendar-next-month-btn::before,
    .cfbgc-calendar-next-year-btn::before {
      border: 1px solid $input-color;
      border-width: 1.5px 0 0 1.5px;
    }

    .cfbgc-calendar-next-year-btn::after {
      border: 1px solid $input-color;
      border-width: 1.5px 0 0 1.5px;
    }
  }

  // 下拉菜单上半部分显示时间的颜色 例：2024年11月
  .cfbgc-calendar-year-select,
  .cfbgc-calendar-month-select {
    color: $input-color;
  }

  // 下拉菜单顶部显示样式 例：开始日期
  .cfbgc-calendar-input-wrap {
    background: $input-bg-color;
    // border-color: #3b466a;
    border-bottom: 2px solid #3b466a;

    .cfbgc-calendar-input {
      background: $input-bg-color;
      color: $input-color;
    }

    .cfbgc-calendar-input::placeholder {
      color: $input-color;
    }
  }

  // 菜单体样式
  .cfbgc-calendar-body {
    // border-color: #3b466a;
    border-bottom: 2px solid #3b466a;

    .cfbgc-calendar-in-range-cell::before {
      // 选中的两个日期中间的日期背景色
      background: $input-color;
    }

    .cfbgc-calendar-date {
      color: #fff;
    }

    // 日期选择器选中时间
    .cfbgc-calendar-selected-day {
      .cfbgc-calendar-date {
        background-color: #2f6edd;
      }
    }

    .cfbgc-calendar-date:hover {
      background-color: #2f6edd;
    }
  }

  .cfbgc-calendar-footer {
    border: none;
  }

  // 月份选择器头部
  .cfbgc-calendar-month-panel-header {
    border-bottom: 2px solid #3b466a;
    background-color: $input-bg-color;
    color: $input-color;

    .cfbgc-calendar-month-panel-prev-year-btn::before,
    .cfbgc-calendar-month-panel-prev-year-btn::after,
    .cfbgc-calendar-month-panel-next-year-btn::before,
    .cfbgc-calendar-month-panel-next-year-btn::after {
      border: 1px solid $input-color;
      border-width: 1.5px 0 0 1.5px;
    }

    .cfbgc-calendar-month-panel-year-select {
      color: #fff;
    }
  }

  // 月份选择器主体
  .cfbgc-calendar-month-panel-body {
    background-color: $input-bg-color;

    .cfbgc-calendar-month-panel-month {
      color: #fff;
    }

    .cfbgc-calendar-month-panel-month:hover {
      background: #2f6edd;
    }
  }

  // 年份选择器
  .cfbgc-calendar-year-panel-header {
    border-bottom: 2px solid #3b466a;
    background-color: $input-bg-color;
    color: $input-color;

    .cfbgc-calendar-year-panel-prev-decade-btn::before,
    .cfbgc-calendar-year-panel-prev-decade-btn::after,
    .cfbgc-calendar-year-panel-next-decade-btn::before,
    .cfbgc-calendar-year-panel-next-decade-btn::after {
      border: 1px solid $input-color;
      border-width: 1.5px 0 0 1.5px;
    }

    .cfbgc-calendar-year-panel-decade-select {
      color: #fff;
    }
  }

  .cfbgc-calendar-year-panel-body {
    background-color: $input-bg-color;

    .cfbgc-calendar-year-panel-year {
      color: #fff;
    }

    .cfbgc-calendar-year-panel-year:hover {
      background: #2f6edd;
    }
  }
}

// 下拉框-下拉菜单样式
.cfbgc-select-dropdown {
  background-color: $input-bg-color;

  // 数据项
  .cfbgc-select-dropdown-menu-item {
    color: white;
  }

  // 选中数据项
  .cfbgc-select-dropdown-menu-item-selected {
    background-color: $input-bg-color;
    color: $input-color;
  }

  // 未选中数据项悬浮效果
  .cfbgc-select-dropdown-menu-item:hover:not(
      .cfbgc-select-dropdown-menu-item-disabled
    ) {
    background-color: #3d4a6f;
  }

  // 清除数据后点击下拉框，第一个下拉选项的样式
  .cfbgc-select-dropdown-menu-item-active:not(
      .cfbgc-select-dropdown-menu-item-disabled
    ) {
    background-color: #3d4a6f;
    color: $input-color;
  }
}

// 表格样式
$table-bg-color1: #1b2242; //表格整体及表头背景色
$table-bg-color2: #24325d; //表格背景色
$table-color: #cce4ff; //表格字体色
$table-font-size: vh(16); //表格字体大小

.cfbgc-table-content,
.cfbgc-table-scroll {
  .cfbgc-table-body {
    background-color: $table-bg-color1 !important;
  }

  .cfbgc-table-header {
    background-color: $table-bg-color1 !important;
  }

  .cfbgc-table-thead {
    th {
      border: none;
      background-color: $table-bg-color1;
      color: $table-color;
      font-size: $table-font-size;
      line-height: vh(20);
      font-weight: 700;
      padding: vh(12.5) vh(4);
    }
  }

  .cfbgc-table-tbody {
    .cfbgc-table-row {
      background-color: $table-bg-color1;

      td {
        background-color: $table-bg-color1;
        color: #fff;
        padding: vh(10.5) vh(4);
        border: none;
        font-size: $table-font-size;
      }

      td:nth-child(1) {
        border-radius: vh(10) 0 0 vh(10);
      }

      td:nth-last-child(1) {
        border-radius: 0 vh(10) vh(10) 0;
      }
    }

    .cfbgc-table-row:nth-of-type(odd) {
      td {
        background-color: $table-bg-color2;
      }
    }

    tr:hover:not(.cfbgc-table-expanded-row):not(.cfbgc-table-row-selected) {
      td {
        background: #4ea0fc;
        color: #8fd4ff;
      }
    }
  }
}

// 表格数据为空时
.cfbgc-table-placeholder {
  background-color: $table-bg-color2;
  border-top: none;
  border-bottom: none;

  .cfbgc-empty-description {
    // 表格数据为空时内容
    color: #fff;
    border: none;
  }
}

$pagination-height: vh(30); //分页器高度
$pagination-line-height: vh(30); //分页器行高
$pagination-bg-color: #4ea0fd; //分页器背景颜色
$pagination-color: #fefeff; //分页器字体颜色
$pagination-size: vh(16); //分页器字体大小

// 表格分页器
.cfbgc-table-pagination {
  margin: vh(8) 0 !important;
  display: flex;

  .cfbgc-pagination-item {
    background-color: $pagination-bg-color;
    border: none;
    line-height: $pagination-line-height;
    height: $pagination-height;
    min-width: $pagination-height;
    width: auto;

    a {
      color: $pagination-color;
      font-size: $pagination-size;
    }
  }

  .cfbgc-pagination-item-active {
    background-color: #1a5ba6;
    font-size: $pagination-size;
  }

  .cfbgc-pagination-item:hover {
    background-color: #3383df;
  }

  .cfbgc-pagination-item-container {
    line-height: $pagination-line-height;
    height: $pagination-height;
    width: $pagination-height;

    .cfbgc-pagination-item-ellipsis {
      color: $pagination-color;
      font-size: $pagination-size;
    }
  }

  .cfbgc-pagination-prev,
  .cfbgc-pagination-next {
    line-height: $pagination-line-height;
    height: $pagination-height;
    width: $pagination-height;

    .cfbgc-pagination-item-link {
      border: none;
      background-color: $pagination-bg-color;
      color: $pagination-color;
      font-size: $pagination-size;
    }
  }

  .cfbgc-pagination-options {
    display: flex;
    align-items: center;
    height: $pagination-height;
  }

  .cfbgc-pagination-options-quick-jumper {
    color: $input-color;
    border-color: $pagination-bg-color;
    height: vh(30);
    font-size: $pagination-size;
    display: flex;
    align-items: center;

    input {
      background-color: $input-bg-color;
      color: $input-color;
      border-color: $pagination-bg-color;
      height: vh(22);
      font-size: $pagination-size;
    }
  }
}

// 加载
.cfbgc-spin-container::after {
  background-color: #0c1738;
}

.cfbgc-spin-blur::after {
  opacity: 0.8;
}

.cfbgc-spin-nested-loading > div > .cfbgc-spin .cfbgc-spin-text {
  text-shadow: none;
  color: white;
}

.cfbgc-spin-dot-item {
  background-color: white;
}

// echars图表的tooltip展示样式
.charsTip {
  display: none;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  // height: 153px;
  background-color: #12182d !important;
  border: none !important;
  padding: 0 vh(10) !important;
  opacity: 0.85 !important;

  .title {
    font-size: vh(16);
    font-family: Source Han Sans CN, Source Han Sans CN-Medium;
    font-weight: 500;
    color: #ffffff;
    line-height: vh(30);
    height: vh(30);
    text-align: left;
  }

  .content {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    height: calc(100% - vh(30));

    .item {
      font-size: vh(14);
      // height: 100%;
      font-family: Helvetica, Helvetica-Regular;
      font-weight: 400;
      // line-height: vh(30);
      display: flex;
      justify-content: space-between;
      align-items: center;

      .label {
        color: #b7c4ca;
      }

      .value {
        color: #ffffff;
      }
    }
  }
}

.charsTipOne {
  background-image: url("~@/assets/images/charsTip/charsTipBgOne.png");
  min-width: vh(119);
  height: vh(61);

  .content {
    .item {
      height: 100%;
      line-height: 100%;
    }
  }
}

.charsTipOne2 {
  background-image: url("~@/assets/images/charsTip/charsTipBgOne2.png");
  min-width: vh(143);
  height: vh(56);

  .content {
    .item {
      height: 100%;
      line-height: 100%;
    }
  }
}

.charsTipTwo {
  background-image: url("~@/assets/images/charsTip/charsTipBgTwo.png");
  min-width: vh(139);
  height: vh(92);

  .content {
    .item {
      height: calc(100% / 2);
      line-height: calc(100% / 2);
    }
  }
}

.charsTipThree {
  background-image: url("~@/assets/images/charsTip/charsTipBgThree.png");
  min-width: vh(146);
  height: vh(126);

  .content {
    .item {
      height: calc(100% / 3);
      line-height: calc(100% / 3);
    }
  }
}

.charsTipFour {
  background-image: url("~@/assets/images/charsTip/charsTipBgFour.png");
  min-width: vh(146);
  height: vh(153);

  .content {
    .item {
      height: calc(100% / 4);
      line-height: calc(100% / 4);
    }
  }
}

.charsTipFive {
  background-image: url("~@/assets/images/charsTip/charsTipBgFive.png");
  min-width: vh(152);
  height: vh(176);

  .content {
    .item {
      height: calc(100% / 5);
      line-height: calc(100% / 5);
    }
  }
}

.charsTipSix,
.charsTipSeven {
  background-image: url("~@/assets/images/charsTip/charsTipBgSix.png");
  min-width: vh(171);
  height: vh(222);
}

.charsTipSix {
  .content {
    .item {
      height: calc(100% / 6);
      line-height: calc(100% / 6);
    }
  }
}

.charsTipSeven {
  .content {
    .item {
      height: calc(100% / 7);
      line-height: calc(100% / 7);
    }
  }
}
