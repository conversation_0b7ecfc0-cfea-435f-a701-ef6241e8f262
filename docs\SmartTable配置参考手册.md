# SmartTable 配置参考手册

## 📋 组件属性 (Props)

### 基础属性

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| `data` | `Array` | `[]` | ✅ | 表格数据数组 |
| `columns` | `Array` | `[]` | ✅ | 列配置数组 |
| `loading` | `Boolean` | `false` | ❌ | 是否显示加载状态 |
| `showIndex` | `Boolean` | `false` | ❌ | 是否显示序号列 |
| `showActions` | `Boolean` | `false` | ❌ | 是否显示操作列 |
| `actionColumnWidth` | `Number/String` | `120` | ❌ | 操作列宽度 |

### 分页属性

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| `pagination` | `Boolean` | `false` | ❌ | 是否启用分页 |
| `pageSize` | `Number` | `10` | ❌ | 每页显示条数 |
| `currentPage` | `Number` | `1` | ❌ | 当前页码 |
| `total` | `Number` | `0` | ❌ | 总条数 |
| `pageSizes` | `Array` | `[10, 20, 50, 100]` | ❌ | 每页显示个数选择器的选项 |

### 高级属性

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| `mergeKeys` | `Array` | `[]` | ❌ | 需要合并的列字段名数组 |
| `summaryConfig` | `Object` | `{}` | ❌ | 汇总配置对象 |

## 🏗️ 列配置 (Column)

### 基础配置

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| `label` | `String` | - | ✅ | 列标题 |
| `prop` | `String` | - | ✅* | 对应数据字段名（有children时可选） |
| `width` | `Number/String` | - | ❌ | 列宽度 |
| `minWidth` | `Number` | `120` | ❌ | 最小列宽度 |
| `align` | `String` | `'center'` | ❌ | 对齐方式：`left`/`center`/`right` |
| `fixed` | `String` | - | ❌ | 列固定位置：`left`/`right` |

### 显示配置

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| `formatter` | `Function` | - | ❌ | 格式化函数：`(row, value) => string` |
| `children` | `Array` | - | ❌ | 子列配置（多级表头） |

### 交互配置

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| `editable` | `Boolean` | `false` | ❌ | 是否可编辑 |
| `clickable` | `Boolean` | `false` | ❌ | 是否可点击 |

### 编辑配置

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| `editType` | `String` | - | ❌ | 编辑类型：`date` |
| `editComponent` | `String` | `'el-input'` | ❌ | 编辑组件名称 |
| `options` | `Array` | - | ❌ | 下拉选项：`[{label, value}]` |
| `validation` | `Object` | - | ❌ | 验证规则配置 |
| `dateConfig` | `Object` | - | ❌ | 日期配置（editType为date时） |

## ✅ 验证规则 (Validation)

### 通用配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `type` | `String` | `'text'` | 验证类型：`text`/`number`/`decimal`/`regex` |
| `required` | `Boolean` | `false` | 是否必填 |
| `errorMessage` | `String` | - | 自定义错误信息 |

### 数字类型 (number/decimal)

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `min` | `Number` | - | 最小值 |
| `max` | `Number` | - | 最大值 |
| `precision` | `Number` | - | 小数位数（仅decimal类型） |

### 文本类型 (text)

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `minLength` | `Number` | - | 最小长度 |
| `maxLength` | `Number` | - | 最大长度 |

### 正则类型 (regex)

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `pattern` | `String` | - | 正则表达式字符串 |

### 验证示例

```javascript
// 必填文本
validation: {
  type: "text",
  required: true,
  minLength: 2,
  maxLength: 20,
  errorMessage: "请输入2-20个字符",
}

// 数字范围
validation: {
  type: "number",
  required: true,
  min: 0,
  max: 100,
  errorMessage: "请输入0-100之间的整数",
}

// 小数精度
validation: {
  type: "decimal",
  precision: 2,
  min: 0,
  errorMessage: "请输入大于0的金额，最多2位小数",
}

// 邮箱验证
validation: {
  type: "regex",
  pattern: "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$",
  errorMessage: "请输入有效的邮箱地址",
}

// 手机号验证
validation: {
  type: "regex",
  pattern: "^1[3-9]\\d{9}$",
  errorMessage: "请输入有效的手机号码",
}
```

## 📅 日期配置 (DateConfig)

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `type` | `String` | `'date'` | 日期类型：`date`/`month`/`year` |
| `format` | `String` | `'yyyy-MM-dd'` | 显示格式 |
| `valueFormat` | `String` | `'yyyy-MM-dd'` | 值格式 |
| `placeholder` | `String` | `'请选择日期'` | 占位符文本 |
| `minDate` | `String` | - | 最小日期 |
| `maxDate` | `String` | - | 最大日期 |

### 日期配置示例

```javascript
// 日期选择
dateConfig: {
  type: "date",
  format: "yyyy年MM月dd日",
  valueFormat: "yyyy-MM-dd",
  placeholder: "请选择日期",
  minDate: "2020-01-01",
  maxDate: "2030-12-31",
}

// 月份选择
dateConfig: {
  type: "month",
  format: "yyyy年MM月",
  valueFormat: "yyyy-MM",
  placeholder: "请选择月份",
}

// 年份选择
dateConfig: {
  type: "year",
  format: "yyyy年",
  valueFormat: "yyyy",
  placeholder: "请选择年份",
}
```

## 📊 汇总配置 (SummaryConfig)

### 基础配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `groupField` | `String` | `''` | 分组字段名 |
| `sumColumns` | `Array` | `[]` | 需要求和的列字段名数组 |

### 显示控制

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `showSubTotal` | `Boolean` | `false` | 是否显示小计 |
| `showGrandTotal` | `Boolean` | `false` | 是否显示总计 |
| `showCustomSubTotal` | `Boolean` | `false` | 是否显示自定义小计 |
| `showCustomGrandTotal` | `Boolean` | `false` | 是否显示自定义总计 |

### 文本配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `subTotalText` | `String` | `'小计'` | 小计文本 |
| `grandTotalText` | `String` | `'合计'` | 总计文本 |
| `customSubTotalText` | `String` | `'自定义小计'` | 自定义小计文本 |
| `customGrandTotalText` | `String` | `'自定义总计'` | 自定义总计文本 |

### 合并配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `subTotalTextMergeColumns` | `Array` | `[]` | 小计文本合并的列 |
| `grandTotalTextMergeColumns` | `Array` | `[]` | 总计文本合并的列 |
| `customSubTotalTextMergeColumns` | `Array` | `[]` | 自定义小计文本合并的列 |
| `customGrandTotalTextMergeColumns` | `Array` | `[]` | 自定义总计文本合并的列 |

### 样式配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `summaryRowClass` | `String` | `'summary-row'` | 汇总行样式类名 |

### 自定义计算

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `customGrandTotalData` | `Function/Object` | `null` | 自定义总计数据 |
| `customSubTotalData` | `Function/Object` | `null` | 自定义小计数据 |

### 汇总配置示例

```javascript
// 基础汇总
summaryConfig: {
  groupField: "category",
  sumColumns: ["amount", "quantity"],
  showSubTotal: true,
  showGrandTotal: true,
  subTotalText: "小计",
  grandTotalText: "合计",
  subTotalTextMergeColumns: ["name"],
  grandTotalTextMergeColumns: ["category", "name"],
}

// 自定义汇总计算
summaryConfig: {
  groupField: "region",
  sumColumns: ["sales"],
  showCustomGrandTotal: true,
  customGrandTotalText: "平均值",
  customGrandTotalData: (data) => ({
    sales: data.reduce((sum, row) => sum + row.sales, 0) / data.length,
  }),
}
```

## 🎯 事件 (Events)

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `cell-change` | `{row, prop, index, value}` | 单元格值改变时触发 |
| `cell-click` | `{row, prop, index, value}` | 单元格点击时触发 |
| `page-change` | `{pageSize, currentPage}` | 分页改变时触发 |
| `edit` | `row` | 编辑按钮点击时触发 |
| `delete` | `row` | 删除按钮点击时触发 |

## 🎨 插槽 (Slots)

| 插槽名 | 作用域参数 | 说明 |
|--------|-----------|------|
| `actions` | `{row, $index}` | 操作列内容 |
| `[prop]` | `{row, $index}` | 自定义列内容（prop为列字段名） |

### 插槽使用示例

```vue
<OgwTable :columns="columns" :data="tableData">
  <!-- 操作列插槽 -->
  <template #actions="{ row, $index }">
    <el-button type="text" @click="edit(row)">编辑</el-button>
    <el-button type="text" @click="delete(row)">删除</el-button>
  </template>
  
  <!-- 自定义列内容插槽 -->
  <template #status="{ row }">
    <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
      {{ row.status === 'active' ? '激活' : '禁用' }}
    </el-tag>
  </template>
</OgwTable>
```

## 🎨 主题配置

### 主题类型

- `default` - 默认主题
- `tint` - 浅色主题  
- `dark` - 深色主题

### 主题切换

```javascript
// 设置主题
document.documentElement.setAttribute("data-theme", "tint");

// 获取当前主题
const currentTheme = document.documentElement.getAttribute("data-theme");
```

### 自定义样式类

```css
/* 自定义汇总行样式 */
.custom-summary-row {
  background-color: #f5f7fa;
  font-weight: bold;
  color: #303133;
}

/* 自定义可编辑单元格样式 */
.custom-editable-cell:hover {
  background-color: #ecf5ff;
  border: 1px solid #409eff;
}
```

---

**完整配置参考，助力高效开发！** 📚
