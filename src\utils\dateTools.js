/**
 * 获取今年上一个月
 * @returns 前一个月份
 */
const getPreMonth = () => {
    let date = new Date();
    let year = date.getFullYear();
    let month = date.getMonth();
    if (month === 0) {
        year = year - 1;
        month = 12;
    }
    month = month < 10 ? "0" + month : month;
    return `${year}-${month}`;
};

/**
 * 获取当前日期
 * @returns 
 */
const getDay = () => {
    let date = new Date();
    let year = date.getFullYear();
    let month = date.getMonth() + 1;
    let day = date.getDate();
    month = month < 10 ? "0" + month : month;
    day = day < 10 ? "0" + day : day;
    return `${year}-${month}-${day}`;
}


export {getPreMonth, getDay};