<template>
  <div class="pdf-viewer">
    <canvas ref="pdfCanvas" class="pdf-canvas"></canvas>
    <div class="toolbar">
      <button @click="goPrev" :disabled="pageNum <= 1">上一页</button>
      <span>{{ pageNum }} / {{ totalPages }}</span>
      <button @click="goNext" :disabled="pageNum >= totalPages">下一页</button>
      <button @click="zoomIn">放大</button>
      <button @click="zoomOut">缩小</button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    pdfUrl: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      pdfDoc: null,
      pageNum: 1,
      totalPages: 0,
      scale: 1.5,
      isRendering: false
    };
  },
  mounted() {
    this.loadPdf();
  },
  methods: {
    async loadPdf() {
      try {
        const loadingTask = window.pdfjsLib.getDocument(this.pdfUrl);
        this.pdfDoc = await loadingTask.promise;
        this.totalPages = this.pdfDoc.numPages;
        this.renderPage(this.pageNum);
      } catch (err) {
        console.error('PDF 加载失败：', err);
      }
    },
    async renderPage(num) {
      this.isRendering = true;
      const page = await this.pdfDoc.getPage(num);
      const viewport = page.getViewport({ scale: this.scale });

      const canvas = this.$refs.pdfCanvas;
      const ctx = canvas.getContext('2d');
      canvas.height = viewport.height;
      canvas.width = viewport.width;

      const renderContext = {
        canvasContext: ctx,
        viewport: viewport
      };

      await page.render(renderContext).promise;
      this.isRendering = false;
    },
    goPrev() {
      if (this.pageNum <= 1 || this.isRendering) return;
      this.pageNum--;
      this.renderPage(this.pageNum);
    },
    goNext() {
      if (this.pageNum >= this.totalPages || this.isRendering) return;
      this.pageNum++;
      this.renderPage(this.pageNum);
    },
    zoomIn() {
      this.scale += 0.2;
      this.renderPage(this.pageNum);
    },
    zoomOut() {
      if (this.scale > 0.4) {
        this.scale -= 0.2;
        this.renderPage(this.pageNum);
      }
    }
  }
};
</script>

<style scoped>
.pdf-viewer {
  text-align: center;
  height: 100%;
}
.toolbar {
  margin-bottom: 10px;
}
.pdf-canvas {
  border: 1px solid #ccc;
}
</style>
