# SmartTable 快速开始指南

## 🚀 5分钟上手 SmartTable

SmartTable 是一个功能强大的 Vue 表格组件，支持在线编辑、数据验证、汇总统计等高级功能。

## 📦 基本引入

```javascript
import OgwTable from "@/components/comTable/OgwTable.vue";

export default {
  components: {
    OgwTable,
  },
};
```

## 🎯 最简示例

```vue
<template>
  <OgwTable :columns="columns" :data="tableData" />
</template>

<script>
export default {
  data() {
    return {
      columns: [
        { label: "姓名", prop: "name" },
        { label: "年龄", prop: "age" },
        { label: "职位", prop: "position" },
      ],
      tableData: [
        { name: "张三", age: 25, position: "前端工程师" },
        { name: "李四", age: 30, position: "后端工程师" },
      ],
    };
  },
};
</script>
```

## ✏️ 可编辑表格

```vue
<template>
  <OgwTable 
    :columns="columns" 
    :data="tableData" 
    @cell-change="handleChange"
  />
</template>

<script>
export default {
  data() {
    return {
      columns: [
        { label: "姓名", prop: "name", editable: true },
        { 
          label: "年龄", 
          prop: "age", 
          editable: true,
          validation: {
            type: "number",
            min: 18,
            max: 65,
          }
        },
        {
          label: "部门",
          prop: "department",
          editable: true,
          options: [
            { label: "技术部", value: "tech" },
            { label: "产品部", value: "product" },
          ],
        },
      ],
      tableData: [
        { name: "张三", age: 25, department: "tech" },
      ],
    };
  },
  methods: {
    handleChange({ row, prop, value }) {
      console.log(`${prop} 改变为: ${value}`);
      // 保存到后端
      this.saveData(row);
    },
  },
};
</script>
```

## 📊 带汇总的表格

```vue
<template>
  <OgwTable 
    :columns="columns" 
    :data="tableData"
    :summary-config="summaryConfig"
    :merge-keys="['category']"
  />
</template>

<script>
export default {
  data() {
    return {
      columns: [
        { label: "类别", prop: "category" },
        { label: "产品", prop: "product" },
        { label: "销售额", prop: "sales" },
        { label: "数量", prop: "quantity" },
      ],
      summaryConfig: {
        groupField: "category",
        sumColumns: ["sales", "quantity"],
        showSubTotal: true,
        showGrandTotal: true,
        subTotalText: "小计",
        grandTotalText: "合计",
      },
      tableData: [
        { category: "电子产品", product: "手机", sales: 10000, quantity: 50 },
        { category: "电子产品", product: "电脑", sales: 20000, quantity: 20 },
        { category: "服装", product: "T恤", sales: 5000, quantity: 100 },
      ],
    };
  },
};
</script>
```

## 📄 分页表格

```vue
<template>
  <OgwTable 
    :columns="columns" 
    :data="tableData"
    :pagination="true"
    :page-size="pageSize"
    :current-page="currentPage"
    :total="total"
    :loading="loading"
    @page-change="handlePageChange"
  />
</template>

<script>
export default {
  data() {
    return {
      columns: [
        { label: "ID", prop: "id" },
        { label: "姓名", prop: "name" },
        { label: "邮箱", prop: "email" },
      ],
      tableData: [],
      pageSize: 10,
      currentPage: 1,
      total: 0,
      loading: false,
    };
  },
  methods: {
    async handlePageChange({ pageSize, currentPage }) {
      this.pageSize = pageSize;
      this.currentPage = currentPage;
      await this.loadData();
    },
    async loadData() {
      this.loading = true;
      try {
        const response = await this.$api.getUsers({
          page: this.currentPage,
          size: this.pageSize,
        });
        this.tableData = response.data;
        this.total = response.total;
      } finally {
        this.loading = false;
      }
    },
  },
  mounted() {
    this.loadData();
  },
};
</script>
```

## 🎨 主题切换

```javascript
// 设置浅色主题
document.documentElement.setAttribute("data-theme", "tint");

// 设置深色主题
document.documentElement.setAttribute("data-theme", "dark");
```

## 🔧 常用配置

### 验证规则

```javascript
// 数字验证
validation: {
  type: "number",
  required: true,
  min: 0,
  max: 100,
  errorMessage: "请输入0-100之间的数字",
}

// 小数验证
validation: {
  type: "decimal",
  precision: 2,
  min: 0,
}

// 文本验证
validation: {
  type: "text",
  required: true,
  minLength: 2,
  maxLength: 20,
}

// 正则验证
validation: {
  type: "regex",
  pattern: "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$",
  errorMessage: "请输入有效的邮箱地址",
}
```

### 日期编辑

```javascript
{
  label: "日期",
  prop: "date",
  editable: true,
  editType: "date",
  dateConfig: {
    type: "date", // 'date' | 'month' | 'year'
    format: "yyyy-MM-dd",
    valueFormat: "yyyy-MM-dd",
    placeholder: "请选择日期",
  },
}
```

### 多级表头

```javascript
{
  label: "销售数据",
  children: [
    { label: "本月", prop: "thisMonth" },
    { label: "上月", prop: "lastMonth" },
    { label: "同比", prop: "yearOverYear" },
  ],
}
```

## 🎯 核心特性

- ✅ **在线编辑**: 支持文本、数字、日期、下拉选择等编辑类型
- ✅ **数据验证**: 内置多种验证规则，支持自定义验证
- ✅ **汇总统计**: 支持分组汇总、小计、总计
- ✅ **单元格合并**: 自动合并相同值的单元格
- ✅ **分页功能**: 内置分页组件
- ✅ **主题支持**: 支持浅色/深色主题切换
- ✅ **操作列**: 可配置的操作按钮
- ✅ **多级表头**: 支持复杂的表头结构
- ✅ **插槽支持**: 灵活的内容自定义

## 📚 更多文档

- [完整使用文档](./SmartTable组件使用文档.md) - 详细的API参考和高级用法
- [组件源码](./src/components/comTable/OgwTable.vue) - 查看组件实现

## 🤝 技术支持

如果在使用过程中遇到问题，请：

1. 查看[常见问题](./SmartTable组件使用文档.md#8-常见问题)
2. 参考[最佳实践](./SmartTable组件使用文档.md#9-最佳实践)
3. 查看组件源码了解实现细节

---

**开始使用 SmartTable，让表格开发更简单！** 🎉
