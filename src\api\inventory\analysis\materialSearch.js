
import {instance} from "../../config";

// 库存物资列表查询
export function sapInfoDay_list(data) {
    return instance({
        url: '/im-ls/sapInfoDay',
        method: "POST",
        data
    });
}


//获取统计结果
export function sapInfoDay_stats(data) {
    return instance({
        url: '/im-ls/sapInfoDay/stats',
        method: "POST",
        data
    });
}

//库存物资列表导出
export function sapInfoDay_export(data) {
    return instance({
        url: '/im-ls/sapInfoDay/export',
        method: "POST",
        responseType: "blob",
        data
    });
}



//根据物料编号查询信息
export function sapInfoDay_unitPrice(params) {
    return instance({
        url: '/im-ls/sapInfoDay/unitPrice',
        method: "GET",
        params
    });
}

//安全/非安全库存作业公司级别分布
export function sapInfoDay_safeDistr(params) {
    return instance({
        url: '/im-ls/sapInfoDay/safeDistr',
        method: "GET",
        params
    });
}
//库龄结构作业公司级别分布
export function sapInfoDay_storageYearDistr(params) {
    return instance({
        url: '/im-ls/sapInfoDay/storageYearDistr',
        method: "GET",
        params
    });
}
