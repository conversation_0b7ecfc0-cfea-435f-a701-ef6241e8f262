<template>
  <div id="header">
    <div class="option" v-for="(item, index) in title" :key="index">
      <div
        class="option-item"
        :class="{ active: isActive === index, hasChildren: item.children }"
        @click="item.children ? null : changeActive(item, index)"
        @mouseenter="showSubmenu = index"
        @mouseleave="showSubmenu = null"
      >
        <div class="option-item-icon"></div>
        <div class="option-item-text">{{ item.text }}</div>
        <!-- 下拉箭头 -->
        <div v-if="item.children" class="arrow">&#9662;</div>

        <!-- 子菜单 -->
        <div v-if="item.children && showSubmenu === index" class="submenu">
          <div
            class="submenu-item"
            v-for="(sub, subIndex) in item.children"
            :key="subIndex"
            @click="changeActive(sub, index)"
          >
            {{ sub.text }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Header",
  props: {
    title: {
      type: Array,
    },
  },
  data() {
    return {
      isActive: 0,
      showSubmenu: null,
    };
  },
  mounted() {
    this.setActiveByRoute();
  },
  watch: {
    $route() {
      this.setActiveByRoute();
    },
  },
  methods: {
    setActiveByRoute() {
      this.title.forEach((item, index) => {
        if (item.path === this.$route.name) {
          this.isActive = index;
        } else if (item.children) {
          const match = item.children.find(
            (sub) => sub.path === this.$route.name
          );
          if (match) this.isActive = index;
        }
      });
    },
    changeActive(item, parentIndex) {
      if (!item.isClick) {
        this.$message.warning("暂未开放");
        return;
      } else {
        this.isActive = parentIndex;
        this.$router.push({ name: item.path });
      }
    },
  },
};
</script>
<style lang="scss" scoped>
#header {
  width: 100%;
  display: flex;
  justify-content: center;
  background-size: 100% 100%;
  margin-top: 24px;
  margin-bottom: 16px;

  .option {
    padding-bottom: 28px;
    margin-right: 40px;

    .option-item {
      position: relative;
      display: flex;
      align-items: center;
      height: 44px;
      box-sizing: border-box;
      cursor: pointer;

      .option-item-icon {
        width: 44px;
        height: 44px;
        background-size: 100% 100%;
      }

      .option-item-text {
        font-size: 16px;
        font-weight: normal;
        line-height: 24px;
        letter-spacing: normal;
        padding-left: 8px;

        white-space: nowrap; // 不换行
        overflow: hidden; // 溢出隐藏
        text-overflow: ellipsis; // 显示省略号
        max-width: 120px;
      }

      .arrow {
        margin-left: 4px;
        font-size: 20px;
      }

      .submenu {
        position: absolute;
        top: 100%;
        left: 10px;
        min-width: 120px;
        border: 1px solid #ddd;
        z-index: 10;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

        font-family: Source Han Sans;
        font-size: 16px;
      }

      .submenu-item {
        padding: 8px 12px;
        cursor: pointer;
        white-space: nowrap;
      }
    }
  }
}

[data-theme="tint"] .option-item:hover {
  #option-item-text {
    color: #1677ff;
  }
}

[data-theme="tint"] .active {
  .option-item-icon {
    background-image: url("@/assets/tableicon/headeritem-tinticon-active.png");
  }

  .option-item-text {
    color: #f5222d;
  }
}

[data-theme="dark"] .option-item:hover {
  .option-item-text {
    color: #fff !important;
  }
}

[data-theme="dark"] .active {
  .option-item-icon {
    background-image: url("@/assets/tableicon/headeritem-darkicon-active.png");
  }

  .option-item-text {
    background: linear-gradient(180deg, #ffffff 0%, #ff7463 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
  }
}

[data-theme="tint"] #header {
  background-image: url("@/assets/tableicon/header-tintbg.png");
  .option-item-icon {
    background-image: url("@/assets/tableicon/headeritem-tinticon.png");
  }
  .option-item-text {
    color: rgba(0, 0, 0, 0.65);
  }
  .arrow {
    color: rgba(0, 0, 0, 0.65);
  }

  .submenu {
    background-color: #ecf7ff;
    color: rgba(0, 0, 0, 0.65);
    border-color: #6ab2ff !important;

    .submenu-item:hover {
      background-color: #1c387c;
      color: #fff;
    }
  }
}

[data-theme="dark"] #header {
  background-image: url("@/assets/tableicon/header-darkbg.png");
  .option-item-icon {
    background-image: url("@/assets/tableicon/headeritem-darkicon.png");
  }
  .option-item-text {
    color: #2ac3ff;
  }

  .arrow {
    color: #2ac3ff;
  }

  .submenu {
    background-color: #1b2242;
    color: #2ac3ff;
    border-color: #327dff !important;

    .submenu-item:hover {
      background-color: #1c387c;
      color: #fff;
    }
  }
}
</style>
