<template>
  <div class="prod-plan">
    <div class="search-box">
      <OgwSearch
        :fields="searchFields"
        v-model="searchForm"
        @search="handleSearch"
        @reset="handleReset"
      ></OgwSearch>
      <div class="btn-group">
        <el-button type="primary">新增</el-button>
      </div>
    </div>
    <div class="table-container">
      <OgwTable
        :columns="columns"
        :data="tableData"
        :merge-keys="['planYear','deviceNameType']"
        :page-size="10"
        :current-page="1"
        :summary-config="exSummaryConfig"
        @cell-click="handleCellClick"
      >
      </OgwTable>
    </div>
  </div>
</template>
<script>
import OgwSearch from "@/components/comTable/OgwSearch.vue";
import OgwTable from "@/components/comTable/OgwTable.vue";
import { getProd } from "@/api/common.js";
export default {
  name: "prodPlan",
  components: {
    OgwSearch,
    OgwTable,
  },
  mounted() {
    window.document.documentElement.setAttribute("data-theme", "tint");
    this.getProdList();
  },
  watch: {
    searchForm: {
      handler(val) {
        this.deviceOptions = this.orgList.map((item) => {
          if (item.orgId === val.orgId) {
            return item.children;
          }
        });
        this.searchFields[1].options = this.deviceOptions.flat(Infinity);
      },
      deep: true,
    },
  },
  data() {
    return {
      orgList: [],
      deviceOptions: [],
      searchForm: {
        orgId: "",
        deviceNameCode: "",
        productType: "",
      },
      exSummaryConfig: {
        groupField: "deviceNameType", // 分组字段
        sumColumns: ["planAmount1"], // 需要计算的列
        grandTotalText: "合计",
        subTotalText: "小计",
        showSubTotal: true, // 是否显示小计
        showGrandTotal: true, // 是否显示总计
        subTotalTextMergeColumns: ["productType", "unit"], // 小计合并的列
        grandTotalTextMergeColumns: ["planYear", "deviceNameType", "productType", "unit"], // 总计合并的列
      },
      tableData: [
        {
          planYear: "2023",
          deviceNameType: "崖城13-1",
          productType: "甲产品",
          unit: "吨",
          planAmount1: 1000,
          realAmount1: 800,
          planAmount2: 1200,
          realAmount2: 1000,
          planAmount3: 1500,
          realAmount3: 1300,
          planAmount4: 1800,
          realAmount4: 1600,
          planAmount5: 2000,
          realAmount5: 1800,
          planAmount6: 2200,
          realAmount6: 2000,
          planAmount7: 2400,
          realAmount7: 2200,
          planAmount8: 2600,
          realAmount8: 2400,
          planAmount9: 2800,
          realAmount9: 2600,
          planAmount10: 3000,
          realAmount10: 2800,
          planAmount11: 3200,
          realAmount11: 3000,
          planAmount12: 3400,
          realAmount12: 3200,
          totalAmount: 20400,
        },
        {
          deviceNameType: "崖城13-1",
          productType: "甲产品",
          unit: "吨",
          planAmount1: 1000,
          realAmount1: 800,
          planAmount2: 1200,
          realAmount2: 1000,
          planAmount3: 1500,
          realAmount3: 1300,
          planAmount4: 1800,
          realAmount4: 1600,
          planAmount5: 2000,
          realAmount5: 1800,
          planAmount6: 2200,
          realAmount6: 2000,
          planAmount7: 2400,
          realAmount7: 2200,
          planAmount8: 2600,
          realAmount8: 2400,
          planAmount9: 2800,
          realAmount9: 2600,
          planAmount10: 3000,
          realAmount10: 2800,
          planAmount11: 3200,
          realAmount11: 3000,
          planAmount12: 3400,
          realAmount12: 3200,
          totalAmount: 20400,
        },
        {
          planYear: "2023",
          deviceNameType: "崖城13-2",
          productType: "甲产品",
          unit: "吨",
          planAmount1: 1000,
          realAmount1: 800,
          planAmount2: 1200,
          realAmount2: 1000,
          planAmount3: 1500,
          realAmount3: 1300,
          planAmount4: 1800,
          realAmount4: 1600,
          planAmount5: 2000,
          realAmount5: 1800,
          planAmount6: 2200,
          realAmount6: 2000,
          planAmount7: 2400,
          realAmount7: 2200,
          planAmount8: 2600,
          realAmount8: 2400,
          planAmount9: 2800,
          realAmount9: 2600,
          planAmount10: 3000,
          realAmount10: 2800,
          planAmount11: 3200,
          realAmount11: 3000,
          planAmount12: 3400,
          realAmount12: 3200,
          totalAmount: 20400,
        },
      ],
    };
  },
  computed: {
    columns() {
      const months = [
        "一月",
        "二月",
        "三月",
        "四月",
        "五月",
        "六月",
        "七月",
        "八月",
        "九月",
        "十月",
        "十一月",
        "十二月",
      ];

      // 生成月份列
      const monthColumns = months.map((month, index) => ({
        label: month,
        children: [
          { label: "计划量", prop: `planAmount${index + 1}` },
          { label: "实际量", prop: `realAmount${index + 1}` },
        ],
      }));

      return [
        { label: "计划年份", prop: "planYear" },
        { label: "装置平台", prop: "deviceNameType" },
        { label: "产品类型", prop: "productType" },
        { label: "单位", prop: "unit", editable: true },
        ...monthColumns,
        { label: "总计", prop: "totalAmount", fixed: "right" },
      ];
    },
    searchFields() {
      return [
        {
          label: "组织机构:",
          prop: "orgId",
          type: "select",
          options: this.orgList.map((item) => {
            return {
              label: item.orgName,
              value: item.orgId,
            };
          }),
        },
        {
          label: "平台名称:",
          prop: "deviceNameCode",
          type: "select",
          options: null,
        },
        {
          label: "产品类型:",
          prop: "productType",
          type: "select",
          options: [],
        },
      ];
    },
  },
  methods: {
    handleSearch() {
      console.log(this.searchForm);
    },
    handleReset(value) {
      // 重置搜索表单
      this.searchForm = {
        planYear: "",
        deviceNameCode: "",
      };
      console.log("Reset:", this.searchForm);
    },
    handleCellClick(row, column, cell, event) {
      console.log(row, column, cell, event);
    },
    saveRow(row) {
      console.log(row);
    },
    async getProdList() {
      const res = await getProd();
      if (res.code === 200) {
        this.orgList = res.data.map((item) => ({
          orgId: item.orgId,
          orgName: item.orgName,
          children: item.children.map((child) => ({
            value: child.hzNo,
            label: child.deptName,
          })),
        }));
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.prod-plan {
  .search-box {
    margin: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .btn-group {
      margin-bottom: 10px;
      text-align: right;
    }
  }
  .table-container {
    padding: 10px;
  }
}

.submitted {
  color: #00b42a;
}

.saved {
  color: #1677ff;
}
</style>
