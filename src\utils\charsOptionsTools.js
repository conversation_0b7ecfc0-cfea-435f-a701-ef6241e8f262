const setCharsTip = (data) => {
    let str = "";
    if (data) {
        str = `<p class='title'>${data[0].name}</p><div class='content'>`;
        data.forEach((item) => {
            str += `<p class='item'><span class='label'>${item.seriesName}</span><span class='value'>${item.value}</span></p>`;
        });
        str += "</div>";
    }
    return str;
}
// 当有空值存在的时候，使用该方法
const setEmptyCharsTip = (data) => {
    let str = "";
    if (data) {
        const valMap = {};
        str = `<p class='title'>${data[0].name}</p><div class='content'>`;
        data.forEach((item) => {
            if (!item.value || valMap[item.seriesName] === item.value) {
            } else {
                str += `<p class='item'><span class='label'>${item.seriesName}</span><span class='value'>${item.value}</span></p>`;
            }
        });
        str += "</div>";
    }
    return str;
}
const setPieCharsTip = (data) => {
    const str = `<p class='title'>${data.seriesName}</p><div class='content'><p class='item'><span class='label'>${data.name}</span><span class='value'>${data.value}</span></p></div>`;
    return str;
}
const setWidth = (size) => {
    let clientWidth =
        window.innerWidth ||
        document.documentElement.clientWidth ||
        document.body.clientWidth;
    if (!clientWidth) return size;
    let scale = clientWidth / 1920;
    return Number((size * scale).toFixed(3));
};
const setHeight = (size) => {
    let clientHeight =
        window.innerHeight ||
        document.documentElement.clientHeight ||
        document.body.clientHeight;
    if (!clientHeight) return size;
    let scale = clientHeight / 1080;
    return Number((size * scale).toFixed(3));
};
const colorList = [
    [
        "#4AD8CE",
        "#60FD84",
        "#F7B452",
        "#F25742",
        "#52D2FD",
        "#7352FB",
        "#fb9352",
        "#4EFD84",
        "#3FB4FF",],
    [
        "#3FB4FF",
        "#4EFD84",
        "#fb9352",
        "#f38338",
        "#52d2fd",
        "#f7b452",
        "#7352fb",
        "#f25742",
        "#527ffb",
    ]
];
const dynamicPosition = (point, params, dom, rect, size) => {
    console.log('tooltip位置', point, params, dom, rect, size);
    if (!rect) {
        // 如果 rect 为 null，使用默认位置
        return [point[0] + 10, point[1] + 10];
    }
    // point: 鼠标位置 [x, y]
    // size: tooltip 的大小 {width, height}
    // rect: 图表的矩形区域 {x, y, width, height}

    // 默认 tooltip 显示在鼠标右侧
    let x = point[0] + 10; // 向右偏移 10px
    let y = point[1];

    // 如果鼠标在图表左侧，tooltip 显示在右侧
    if (point[0] < rect.width / 2) {
        x = point[0] + 10; // 向右偏移
    } else {
        x = point[0] - size.width - 10; // 向左偏移
    }

    // 如果鼠标在图表下部，tooltip 显示在上部
    if (point[1] > rect.height / 2) {
        y = point[1] - size.height - 10; // 向上偏移
    } else {
        y = point[1] + 10; // 向下偏移
    }

    return [x, y]; // 返回 tooltip 的位置
}
const setConfigurationsColor = ({
    bgColor,
    xLabelColor,
    ySplitLineColor,
    yAxisLabelColor,
    yAxisNameColr,
    titleTextColor,
    legendTextColor
}, setConfigurations, colors = colorList[1]) => {
    console.log('----------------', setConfigurations);

    setConfigurations.forEach((item, index) => {
        const option = item.option
        option.title.textStyle.color = titleTextColor;
        option.legend.textStyle.color = legendTextColor;
        option.xAxis.forEach(e => e.axisLabel.color = xLabelColor);
        option.yAxis.forEach(e => {
            e.nameTextStyle.color = yAxisNameColr;
            e.axisLabel.color = yAxisLabelColor;
            e.splitLine.lineStyle.color = ySplitLineColor;
        });
        const series = option.series;
        if (series.length) {
            series.forEach((e, i) => {
                if (e.type == 'bar') {
                    e.gradient && (e.itemStyle = {
                        borderRadius: [10, 10, 0, 0],
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: colors[i] },
                            { offset: 1, color: bgColor },
                        ]),
                    })
                }
            })
        }
    })
    return setConfigurations
}
export { setWidth, setHeight, setCharsTip, colorList, setPieCharsTip, setEmptyCharsTip, dynamicPosition, setConfigurationsColor };