<template>
  <div class="proPlaning">
    <div class="check-box">
      <el-checkbox-group v-model="checkList">
        <el-checkbox v-for="item in lData" :label="item.name" :key="item.name"></el-checkbox>
      </el-checkbox-group>
    </div>
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  name: "proPlaning",
  data() {
    return {
      checkList: [],
      lData: [
        {
          name: "总产量",
          value: 100,
        },
        {
          name:"崖城13-1",
          value: 20
        },
        {
          name:"崖城13-10",
          value: 30
        },
         {
          name:"陵水25-1",
          value: 90
        },
        {
          name:"陵水17-2",
          value: 50
        },
        {
          name:"文昌16-2",
          value: 80
        }
      ],
      xData: ["2026年", "2027年", "2028年", "2029年", "2030年"],
      yData: [148, 152, 171, 154, 201],
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  methods: {
    initChart() {
      let mychart = echarts.init(this.$refs.chartBox);
      this.option = {
        color: ["#248EFF"], //圆柱体颜色
        tooltip: {
          trigger: "item",
          padding: 1,
          formatter: function (param) {},
        },
        grid: {
          top:'14%',
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: this.xData,
            axisTick: {
              alignWithLabel: true,
            },
            nameTextStyle: {
              color: "#ACC2E2", //文本颜色
            },
            axisLine: {
              lineStyle: {
                color: "rgba(172, 194, 226, 0.2)", //轴线颜色
              },
            },
            axisLabel: {
              textStyle: {
                color: "#ACC2E2", //轴线文本颜色
              },
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "亿方",
            position: "left",
            nameTextStyle: {
              color: "#ACC2E2",
              align: "right",
              padding: [0, 10, 0, 0],
            },
            axisLabel: {
              textStyle: {
                color: "#ACC2E2",
              },
              formatter: "{value}",
            },
            splitLine: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: "储采比",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [0, -5],
            symbolPosition: "end",
            z: 12,
            label: {
              normal: {
                show: false,
              },
            },
            data: this.yData,
          },
          {
            name: "储采比",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [0, 5],
            z: 12,
            data: this.yData,
          },
          {
            name: "储采比",
            type: "bar",
            itemStyle: {
              normal: {
                opacity: 0.7,
              },
            },
            barWidth: "20",
            data: this.yData,
          },
          {
            name: "崖城13-1",
            type: "line",
            data: [45, 52, 88, 42, 95],
            itemStyle: {
              color: "#FF6660", // 设置线条颜色为红色
            },
          },
          {
            name: "崖城13-10",
            type: "line",
            data: [30, 33, 40, 42, 55],
            itemStyle: {
              color: "#F7AE44", // 设置线条颜色为红色
            },
          },
        ],
      };
      mychart.setOption(this.option);
    },
  },
};
</script>
<style lang="scss" scoped>
.proPlaning {
  width: 100%;
  .check-box{
    display: flex;
    padding-left: 16px;
    padding-top: 12px;
  }
  .chart-box {
    width: 100%;
    height: 280px;
  }
}

::v-deep .el-checkbox{
  margin-right: 16px;
  color: #ACC2E2 !important;
}
</style>
