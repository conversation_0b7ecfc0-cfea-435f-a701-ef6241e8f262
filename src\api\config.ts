import axios from "axios";
import { message } from "cfbgc-design";
import qs from "qs";

export const prefix = process.env.VUE_APP_PREFIX;

let request = axios.create({});

request.interceptors.request.use(
  (config) => {
    const access_token = localStorage.getItem("access_token");
    // @ts-ignore
    config!.headers.client_id = process.env.VUE_APP_ID;
    // @ts-ignore
    config!.headers.client_secret = process.env.VUE_APP_ID;
    if (access_token) {
      // @ts-ignore
      config!.headers.Authorization = access_token;
    }
    config.url = prefix + config.url;
    // config.url =  config.url
    const type = config.method;
    const arrayFormat = config.headers.arrayFormat || "indices";
    if (
      type === "post" &&
      config.headers["Content-Type"] ===
        "application/x-www-form-urlencoded; charset=utf-8"
    ) {
      // post请求参数处理
      config.data = qs.stringify(config.data, {
        allowDots: true,
        arrayFormat: arrayFormat,
      });
    } else if (type === "get") {
      // get请求参数处理
      config.paramsSerializer = (params) => {
        console.log(
          "参数处理",
          qs.stringify(params, {
            allowDots: true,
            arrayFormat: arrayFormat,
          })
        );

        return qs.stringify(params, {
          allowDots: true,
          arrayFormat: arrayFormat,
        });
      };
    }
    return config;
  },
  (err) => {
    return Promise.reject(err);
  }
);

request.interceptors.response.use(
  (response) => {
    //拦截响应，做统一处理
    if (response.data.code == 401) {
      message.warn(response.data.msg);
      return Promise.reject(response);
    }
    return response.data;
  },
  //接口错误状态处理，也就是说无响应时的处理
  (error: any) => {
    switch (error.response.status) {
      case 400:
        message.error("请求错误,请稍后重试。若仍然无响应，请联系技术人员");
        break;
      case 401:
        message.error("未授权,即将跳转到登录页");
        setTimeout(() => {
          logOut();
        }, 2000);
        break;
      case 403:
        message.error("拒绝访问,请稍后重试。");
        break;
      case 404:
        message.error("请求地址出错,请稍后重试。");
        break;
      case 408:
        message.error("请求超时,请稍后重试。");
        break;
      case 500:
        message.error("服务器内部错误,请稍后重试。");
        break;
      case 501:
        message.error("服务未实现,请稍后重试。");
        break;
      case 502:
        message.error("网关错误,请稍后重试。");
        break;
      case 503:
        message.error("服务不可用,请稍后重试。");
        break;
      case 504:
        message.error("网关超时,请稍后重试。");
        break;
      case 505:
        message.error("HTTP版本不受支持,请稍后重试。");
        break;
      default:
        break;
    }

    return Promise.reject(error.response.status); // 返回接口返回的错误信息
  }
);

const logOut = () => {
  localStorage.clear();
  history.replaceState({}, "", "/login");
};

export const instance = request;
