const changeRouter = {
  path: "/operating",
  nameCN: "经营管理",
  component: () => import("@/views/operatingAnalysis/index.vue"),
  children: [
    {
      path: "situation",
      name: "situation",
      nameCN: "经营态势",
      component: () => import("@/views/operatingAnalysis/situation/index.vue"),
    },
    {
      path: "oilProduction",
      name: "oilProduction",
      nameCN: "油气产量",
      component: () => import("@/views/operatingAnalysis/mainIncome/oilProduct/index.vue"),
    },
    {
      path: "oilPrice",
      name: "oilPrice",
      nameCN: "油价分析",
      component: () => import("@/views/operatingAnalysis/mainIncome/oilPrice/index.vue"),
    },
    {
      path:"salesRevenue",
      name:"salesRevenue",
      nameCN:"销售收入",
      component: () => import("@/views/operatingAnalysis/mainIncome/salesRevenue/index.vue"),
    },
    {
      path: "costAndExpense",
      name: "costAndExpense",
      nameCN: "成本费用",
      component: () =>
        import("@/views/operatingAnalysis/costAndExpense/index.vue"),
    },
    {
      path: "profitability",
      name: "profitability",
      nameCN: "盈利能力",
      component: () =>
        import("@/views/operatingAnalysis/profitability/index.vue"),
    },
  ],
};

export default changeRouter;
