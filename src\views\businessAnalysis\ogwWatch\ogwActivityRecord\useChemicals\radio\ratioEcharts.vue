<template>
  <div class="ratioEcharts">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  name: "ratioEcharts",
  props: {
    chartsData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      xData: [],
      myChart: null, // 保存图表实例
    };
  },
  watch: {
    chartsData: {
      handler(val) {
        console.log("watch", val);
        this.$nextTick(() => {
          this.initChart();
        });
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    initChart() {
      // 如果图表实例已存在，先销毁
      if (this.myChart) {
        this.myChart.dispose();
      }
      
      this.myChart = echarts.init(this.$refs.chartBox);
      
      // 扩展颜色数组，支持更多线条
      const colors = [
        '#5470c6', '#91cc75', '#fac858', '#ee6666', '#fc8452', '#9a60b4',
        '#ea7ccc', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc',
        '#5470c6', '#91cc75', '#fac858', '#ee6666'
      ];
      
      this.option = {
        tooltip: {
          trigger: "axis",
          formatter: function(params) {
            let result = params[0].axisValue + '<br/>';
            params.forEach(param => {
              // 替换系列名称中的"比率"为"IP1"，"差值"为"Psat"
              let displayName = param.seriesName.replace('比率', 'IP1').replace('差值', 'Psat');
              const unit = param.seriesName.includes('比率') ? '%' : '';
              result += `${param.marker}${displayName}: ${param.value}${unit}<br/>`;
            });
            return result;
          }
        },
        legend: {
          show: false // 隐藏图例
        },
        grid: {
          left: "8%",
          right: "8%",
          bottom: "3%",
          top: "5%", // 减少顶部空间，因为没有图例
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: this.chartsData.xData || [],
          axisTick: {
            show: false,
          },
        },
        yAxis: [
          {
            type: "value",
            name: "IP1",
            position: "left",
            nameTextStyle: {
              color: "#666",
              fontSize: 12
            },
            nameLocation: "middle",
            nameGap: 50,
            axisLine: {
              show: true,
              lineStyle: {
                color: "rgba(172, 194, 226, 0.5)",
              },
            },
            axisTick: {
              show: true,
            },
            axisLabel: {
              color: "#666"
            },
            splitLine: {
              lineStyle: {
                color: "rgba(172, 194, 226, 0.2)",
              },
            },
          },
          {
            type: "value",
            name: "Psat",
            position: "right",
            nameTextStyle: {
              color: "#666",
              fontSize: 12
            },
            nameLocation: "middle",
            nameGap: 60, // 增加距离，让文本离数值远一点
            axisLine: {
              show: true,
              lineStyle: {
                color: "rgba(172, 194, 226, 0.5)",
              },
            },
            axisTick: {
              show: true,
            },
            axisLabel: {
              color: "#666"
            },
            splitLine: {
              show: false,
            },
          },
        ],
        series: (this.chartsData.seriesData || []).map((item, index) => ({
          ...item,
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            width: 2,
            type: 'solid' // 所有线条都使用实线
          },
          itemStyle: {
            color: colors[index % colors.length]
          },
          emphasis: {
            focus: 'series',
            lineStyle: {
              width: 4
            }
          }
        }))
      };
      
      this.myChart.setOption(this.option, true); // 第二个参数true表示不合并配置
    },
  },
  beforeDestroy() {
    if (this.myChart) {
      this.myChart.dispose();
    }
  },
};
</script>
<style lang="scss" scoped>
.ratioEcharts {
  width: 100%;
  .chart-box {
    width: 60%;
    height: 240px;
  }
}
</style>
