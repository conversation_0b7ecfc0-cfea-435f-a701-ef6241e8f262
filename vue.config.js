const { name } = require("./package");
const { proxy } = require("./proxy.config");
const { modifyVars } = require("./antd.json");
const devPort = process.env.VUE_APP_PORT;
const shared = {};
const TerserPlugin = require("terser-webpack-plugin");

module.exports = {
    publicPath: '/fbpApp/',
    devServer: {
        headers: {
            'Access-Control-Allow-Origin': '*',
        },
        port: devPort,
        allowedHosts: 'all',
        client: {
            overlay: false
        },
        proxy: proxy,
    },


  chainWebpack: (config) => {
    config.optimization.delete("splitChunks");
    config
      .plugin("module-federation-plugin")
      .use(require("webpack").container.ModuleFederationPlugin, [
        {
          name: "child",
          remotes: {
            home: "home@http://localhost:8084/remoteEntry.js",
            // microReactFront: "microReactFront@http://localhost:3000/remoteEntry.js",
          },
        },
      ]);
  },
  productionSourceMap: false,
  css: {
    loaderOptions: {
      less: {
        lessOptions: {
          // modifyVars,
          javascriptEnabled: true,
        },
      },
      scss: {
        // @/ 是 src/ 的别名
        // 所以这里假设你有 `src/variables.sass` 这个文件
        // 注意：在 sass-loader v8 中，这个选项名是 "prependData"
        prependData: `@import "~@/style/reset.scss";`,
      },
    },
  },
  configureWebpack: {
    entry: "./src/entry-micro.js",
    output: {
      library: `${name}-[name]`,
      libraryTarget: "umd",
      // jsonpFunction: `webpackJsonp_${name}`,
    },
    optimization: {
      minimizer: [
        new TerserPlugin({
          terserOptions: {
            mangle: true, // 变量名混淆
            compress: {
              drop_console: true, // 移除 console
            },
          },
        }),
      ],
    },
    module: {
      rules: [
        {
          test: /pdf\.worker(\.min)?\.js$/,
          use: {
            loader: "worker-loader",
          },
        },
      ],
    },
  },
};
