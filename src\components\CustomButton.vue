<template>
  <div :class="type" style="display: inline-block">
    <div :class="size + ' ' + color + ' ' + bgcolor + ' ' + border_color">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    size: {
      type: String,
      default: () => {
        return " ";
      },
    },
    border_color: {
      type: String,
      default: () => {
        return " ";
      },
    },
    type: {
      type: String,
      default: () => {
        return " ";
      },
    },
    color: {
      type: String,
      default: () => {
        return " ";
      },
    },
    bgcolor: {
      type: String,
      default: () => {
        return " ";
      },
    },
  },
  name: "Button",
  data() {
    return {};
  },
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  // 方法集合
  methods: {},
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {},
  beforeCreate() {},
  beforeMount() {}, // 生命周期 - 挂载之前
  beforeUpdate() {}, // 生命周期 - 更新之前
  updated() {}, // 生命周期 - 更新之后
  beforeDestroy() {}, // 生命周期 - 销毁之前
  destroyed() {}, // 生命周期 - 销毁完成
  activated() {}, // 如果页面有 keep-alive 缓存功能,这个函数会触发
};
</script>

<style scoped lang="scss">
.normal {
  cursor: pointer;
  color: #ffffff;
  font-size: 14px;

  .small {
    padding: 5px 16px;
    border-radius: 5px;
  }

  .little {
    padding: 0 7px;
    border-radius: 5px;
  }

  .blue {
    background: #0a65c2;
  }

  .yellow {
    background: #f1a33b;
  }

  .green {
    background: #0fb2a1;
  }

  .grey {
    background: #474b6c;
    opacity: 0.5;
  }

  .red {
    background: #f74a4d;
  }
  .purple {
    background: #4851c3;
  }
  .success {
    background: #70b603;
  }
}

.plain {
  cursor: pointer;
  font-size: 14px;

  .small {
    padding: 5px 16px;
    border-radius: 5px;
  }

  .blue {
    border: 1px solid #0a65c2;
    color: #0a65c2;
  }

  .green {
    border: 1px solid #26aea0;
    color: #26aea0;
  }

  .success {
    background: #dfffdb;
  }
}
</style>
