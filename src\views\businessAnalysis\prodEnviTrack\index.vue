<template>
  <div class="prodEnviTrack">
    <div class="content-one">
      <div class="proAbility">
        <ChartBox :title="'公司生产能力'">
          <template v-slot:box-right>
            <DatePicker style="margin-right: 12px" />
          </template>
          <proAbility></proAbility>
        </ChartBox>
      </div>
      <div class="proPlaning">
        <ChartBox :title="title">
          <proPlaning></proPlaning>
        </ChartBox>
      </div>
    </div>
    <div class="content-btns">
      <ButtonBox :buttons="buttons" />
    </div>
    <div class="content-two">
      <ChartBox :title="'油气田平均单井日产气量'" class="gasPro">
        <template v-slot:box-right>
          <YearRange style="margin-right: 12px"></YearRange>
        </template>
        <gasProduction></gasProduction>
      </ChartBox>
      <ChartBox :title="'油气田平均单井日产油量'" class="oilPro">
        <template v-slot:box-right>
          <YearRange style="margin-right: 12px"></YearRange>
        </template>
        <oilProduction></oilProduction>
      </ChartBox>
    </div>
    <div class="content-three">
      <ChartBox :title="'自然递减率/综合递减率'" class="declineRate">
        <template v-slot:box-right>
          <YearRange style="margin-right: 12px"></YearRange>
        </template>
        <declineRate></declineRate>
      </ChartBox>
      <ChartBox :title="'动储量采出程度/储采比'" class="reProsRate">
        <template v-slot:box-right>
          <YearRange style="margin-right: 12px"></YearRange>
        </template>
        <reProsRate></reProsRate>
      </ChartBox>
    </div>
  </div>
</template>
<script>
import DatePicker from "@/views/businessAnalysis/ogw/DatePicker.vue";
import ButtonBox from "./buttonBox.vue";
import proAbility from "./proAbility/index.vue";
import proPlaning from "./proPlaning/index.vue";
import gasProduction from "./gasProduction/index.vue";
import oilProduction from "./oilProduction/index.vue";
import declineRate from "./declineRate/index.vue";
import reProsRate from "./reProsRate/index.vue";
import YearRange from "./YearRange.vue";
export default {
  name: "prodEnviTrack",
  components: {
    DatePicker,
    ButtonBox,
    proAbility,
    proPlaning,
    gasProduction,
    oilProduction,
    declineRate,
    reProsRate,
    YearRange,
  },
  data() {
    return {
      rangeValue: [],
      title: '"十五五"产量规划',
      buttons: ["YC13-1", "YC13-10", "LS25-1", "LS17-2", "文昌16-2"],
    };
  },
};
</script>
<style lang="scss" scoped>
.prodEnviTrack {
  margin: 10px;

  .content-one {
    display: flex;
    justify-content: space-between;

    .proAbility {
      flex: 8;
      margin-right: 10px;
    }

    .proPlaning {
      flex: 4;
    }
  }

  .content-two {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;

    .gasPro {
      flex: 1;
      margin-right: 10px;
    }

    .oilPro {
      flex: 1;
    }
  }

  .content-three {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;

    .declineRate {
      flex: 1;
      margin-right: 10px;
    }

    .reProsRate {
      flex: 1;
    }
  }
}
</style>
