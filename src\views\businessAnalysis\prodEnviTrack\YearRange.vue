<template>
  <div class="year-range">
    <el-date-picker
      :clearable="false"
      v-model="startYear"
      type="year"
      placeholder="开始年份"
      :picker-options="startYearOptions"
      @change="handleYearChange"
    />
    <span class="text">至</span>
    <el-date-picker
      :clearable="false"
      v-model="endYear"
      type="year"
      placeholder="结束年份"
      :picker-options="endYearOptions"
      @change="handleYearChange"
    />
  </div>
</template>

<script>
export default {
  name: "YearRange",

  data() {
    return {
      startYearOptions: "",
      endYearOptions: "",
      startYear: "",
      endYear: "",
    };
  },
  methods: {
    handleYearChange() {
      this.$emit("change");
    },
  },
};
</script>

<style lang="scss" scoped>
.text {
  margin: 0 10px;
  font-family: Source <PERSON> San<PERSON>;
  font-size: 14px;
}

::v-deep .el-date-editor.el-input {
  width: 130px;
}

::v-deep .el-input--prefix .el-input__inner {
  padding-left: 12px;
  height: 32px;
  font-family: Source <PERSON>;
  font-size: 14px;
}

::v-deep .el-input__prefix {
  right: 0;
  left: auto;
}

::v-deep .el-input__icon {
  line-height: 32px;
}

</style>

<style scoped>
[data-theme="dark"] .year-range /deep/ .el-input__inner {
  background-color: #263e78 !important;
  color: #6ba4f4;
  border: none;
}

[data-theme="tint"] .year-range /deep/ .el-input__inner {
  background: #ffffff !important;
  color: rgba(0, 0, 0, 0.65);
  border: none;
}
</style>
