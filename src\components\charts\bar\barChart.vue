<template>
  <div class="chart-container">
    <div class="chart" ref="chart"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
// import "echarts-gl";
export default {
  name: "barChart",
  props: {
    xData: {
      type: Array,
      default: () => [],
    },
    yData: {
      type: Array,
      default: () => [],
    },
    attr: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      statusChart: null,
      option: {},
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  methods: {
    initChart() {
      let mychart = echarts.init(this.$refs.chart);
      this.option = {
        backgroundColor: "#162549",
        color: ["#248EFF"], //圆柱体颜色
        tooltip: {
          trigger: "item",
          borderWidth: 0,
          backgroundColor: "rgba(12, 15, 41, 0.9)",
          extraCssText:
            "box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.5);border-radius: 12px;",
          textStyle: {
            fontFamily: "Source Han Sans",
            color: "#FFFFFF",
            fontSize: 14,
          },
          formatter: function (param) {
            // console.log("param", param);
            return `
            <div>${param.name}</div>
            <div>年度预算  ${param.value}万元</div>
            `;
          },
        },
        grid: {
          left: "26px",
          top: "32px",
        },
        xAxis: [
          {
            type: "category",
            data: this.xData,
            axisTick: {
              alignWithLabel: true,
            },
            nameTextStyle: {
              color: "#ACC2E2", //文本颜色
            },
            axisLine: {
              lineStyle: {
                color: "rgba(172, 194, 226, 0.2)", //轴线颜色
              },
            },
            axisLabel: {
              textStyle: {
                color: "#ACC2E2", //轴线文本颜色
              },
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "万元",
            position: "left",
            nameTextStyle: {
              color: "#ACC2E2",
              align: "right",
            },
            axisLabel: {
              textStyle: {
                color: "#ACC2E2",
              },
              formatter: "{value}",
            },
            splitLine: {
              lineStyle: {
                color: "#0c2c5a",
              },
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "rgba(172, 194, 226, 0.2)",
              },
            },
          },
        ],
        series: [
          {
            name: "",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [0, -5],
            symbolPosition: "end",
            z: 12,
            label: {
              normal: {
                show: false,
              },
            },
            data: this.yData,
          },
          {
            name: "",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [0, 5],
            z: 12,
            data: this.yData,
          },
          {
            type: "bar",
            itemStyle: {
              normal: {
                opacity: 0.7,
              },
            },
            barWidth: "20",
            data: this.yData,
          },
        ],
      };
      mychart.setOption(this.option);
    },
  },
};
</script>
<style lang="less" scoped>
.chart-container {
  width: 100%;
  min-height: 300px;
  flex: 1;
  .chart {
    z-index: 1;
  }
  .chart {
    width: 100%;
    height: 100%;
  }
}
</style>
