
import { instance } from "./config";

// 变更管理-表
export function changeList(data) {
    return instance({
        url: '/eqpt/changeInfo/queryChangeInfo',
        method: "post",
        data
    });
}
// 变更管理-饼图+柱状图+数据
export function changeAll(params) {
    return instance({
        url: '/eqpt/changeInfo/countChangeInfo',
        method: "get",
        params
    });
}
// 变更管理-导出
export function exportFile(data) {
    return instance({
        url: '/eqpt/changeInfo/exportChangeInfo',
        method: "POST",
        responseType: 'blob',
        data
    });
}
// 变更管理-select
export function optionsData() {
    return instance({
        url: '/eqpt/changeInfo/getChangeParam',
        method: "get",
    });
}

// 变更管理预申请
export function postApply(data) {
    return instance({
        url: '/eqpt/changeApply/apply',
        method: "post",
        data
    });
}
// 获取当前用户名装置
export function getUserInfo() {
    return instance({
        url: '/eqpt/changeApply/getUserInfo',
        method: "get",
    });
}
// 变更预申请信息查询
export function queryInfo(params) {
    return instance({
        url: '/eqpt/changeApply/query',
        method: "get",
        params
    });
}