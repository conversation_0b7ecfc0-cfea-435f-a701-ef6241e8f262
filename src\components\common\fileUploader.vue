<template>
  <el-dialog
    title="上传文件"
    :visible.sync="visible"
    width="600px"
    @close="handleClose"
  >
    <el-upload
      class="upload-demo"
      drag
      multiple
      :action="uploadUrl"
      :headers="uploadHeaders"
      :on-error="handleError"
      :on-success="handleSuccess"
      :before-upload="beforeUpload"
      :file-list="fileList"
      :accept="acceptString"
      :data="fileData"
      :on-change="handleFileChange"
      :auto-upload="false"
      ref="upload"
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      <div class="el-upload__tip" slot="tip">
        支持多文件上传，单个文件最大 {{ maxSizeMB }}MB，允许类型：{{
          acceptString
        }}
      </div>
    </el-upload>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="submitUpload">上传</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: "FileUploadDialog",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    fileData: {
      type: Object,
      default: () => ({}),
    },
    uploadUrl: {
      type: String,
      required: true,
    },
    fileTypes: {
      type: Array,
      default: () => [".pdf", ".png", ".jpg", ".jpeg"],
    },
    maxSizeMB: {
      type: Number,
      default: 5,
    },
  },
  data() {
    return {
      fileList: [],
      uploadHeaders: {},
    };
  },
  computed: {
    acceptString() {
      return this.fileTypes.join(",");
    },
  },
  mounted() {
    this.initUploadHeaders();
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        // 每次打开对话框时重新初始化token
        this.initUploadHeaders();
      }
    },
  },
  methods: {
    /**
     * 初始化上传请求头，添加身份验证token
     */
    initUploadHeaders() {
      const access_token = localStorage.getItem("access_token");
      this.uploadHeaders = {
        client_id: process.env.VUE_APP_ID,
        client_secret: process.env.VUE_APP_ID,
      };
      if (access_token) {
        this.uploadHeaders.Authorization = access_token;
      }
    },
    handleFileChange(file, fileList) {
      this.fileList = fileList; // 手动同步文件列表
    },
    handleClose() {
      this.fileList = [];
      this.$emit("update:visible", false);
    },
    beforeUpload(file) {
      const ext = "." + file.name.split(".").pop().toLowerCase();
      const isAllowed = this.fileTypes.includes(ext);
      const isLtMax = file.size / 1024 / 1024 < this.maxSizeMB;

      if (!isAllowed) {
        this.$message.error(
          `文件 "${file.name}" 类型不允许，允许类型：${this.acceptString}`
        );
        return false;
      }

      if (!isLtMax) {
        this.$message.error(
          `文件 "${file.name}" 超过 ${this.maxSizeMB}MB 限制`
        );
        return false;
      }

      return true;
    },
    handleSuccess(response, file) {
      this.$message.success(`文件 "${file.name}" 上传成功`);
      this.$emit("upload-success", response, file);
    },
    handleError(err, file) {
      this.$message.error(`文件 "${file.name}" 上传失败`);
    },
    submitUpload() {
      if (this.fileList.length === 0) {
        this.$message.warning("请先选择要上传的文件");
        return;
      } else {
        this.$refs.upload.submit();
      }
    },
  },
};
</script>

<style scoped>
.upload-demo {
  width: 100%;
}
</style>
