<template>
  <div class="file-preview">
    <!-- iframe 预览（doc/xls/ppt 或 type='office-iframe'） -->
    <iframe
      v-if="useIframe"
      :src="iframeSrc"
      frameborder="0"
      width="100%"
      height="600px"
    ></iframe>

    <!-- PDF 渲染 -->
    <div
      v-else-if="fileType === 'pdf'"
      ref="pdfContainer"
      class="pdf-container"
    ></div>

    <!-- DOCX 渲染 -->
    <div
      v-else-if="fileType === 'docx'"
      v-html="docxHtml"
      class="docx-container"
    ></div>

    <!-- Excel 渲染 -->
    <div v-else-if="fileType === 'excel'">
      <table class="excel-table">
        <tr v-for="(row, rowIndex) in excelData" :key="rowIndex">
          <td v-for="(cell, colIndex) in row" :key="colIndex">{{ cell }}</td>
        </tr>
      </table>
    </div>

    <!-- 不支持 -->
    <div v-else>暂不支持该文件类型</div>
  </div>
</template>

<script>
// import * as pdfjsLib from "pdfjs-dist/build/pdf";
// import pdfjsWorker from "pdfjs-dist/build/pdf.worker.entry";
// import * as XLSX from "xlsx";

// 设置 worker
pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker;

export default {
  props: {
    fileUrl: {
      type: String,
      required: true,
    },
    // ✅ 新增：外部可传 type
    type: {
      type: String,
      default: "", // 可选值：pdf, docx, excel, office-iframe
    },
  },
  data() {
    return {
      fileType: "",
      docxHtml: "",
      excelData: [],
      useIframe: false,
      iframeSrc: "",
    };
  },
  methods: {
    detectFileType() {
      // ✅ 优先使用外部传入的 type
      if (this.type) {
        const lowerType = this.type.toLowerCase();
        if (["pdf", "docx", "excel"].includes(lowerType)) {
          return lowerType;
        }
        if (lowerType === "office-iframe") {
          this.useIframe = true;
          this.iframeSrc = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(
            this.fileUrl
          )}`;
          return "office-iframe";
        }
        return "";
      }

      // ✅ 自动识别（后缀匹配）
      const url = this.fileUrl.toLowerCase();
      if (url.endsWith(".pdf")) return "pdf";
      if (url.endsWith(".docx")) return "docx";
      if (url.endsWith(".xlsx")) return "excel";
      if (
        url.endsWith(".doc") ||
        url.endsWith(".xls") ||
        url.endsWith(".ppt") ||
        url.endsWith(".pptx")
      ) {
        this.useIframe = true;
        this.iframeSrc = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(
          this.fileUrl
        )}`;
        return "office-iframe";
      }
      return "";
    },

    async renderPdf() {
      
    },

    async renderDocx() {
      const response = await fetch(this.fileUrl);
      const arrayBuffer = await response.arrayBuffer();
      const result = await window.mammoth.convertToHtml({ arrayBuffer });
      this.docxHtml = result.value;
    },

    async renderExcel() {
      const res = await fetch(this.fileUrl);
      const data = await res.arrayBuffer();
      const workbook = XLSX.read(data, { type: "array" });
      const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
      this.excelData = XLSX.utils.sheet_to_json(firstSheet, { header: 1 });
    },
  },
  async mounted() {
    // this.fileType = this.detectFileType();
    // if (this.useIframe) return;

    // if (this.fileType === "pdf") await this.renderPdf();
    // else if (this.fileType === "docx") await this.renderDocx();
    // else if (this.fileType === "excel") await this.renderExcel();
  },
};
</script>

<style scoped>
.file-preview {
  border: 1px solid #ddd;
  padding: 10px;
}
.pdf-container canvas {
  display: block;
  margin: 10px auto;
}
.excel-table {
  width: 100%;
  border-collapse: collapse;
}
.excel-table td {
  border: 1px solid #ccc;
  padding: 6px;
}
</style>
