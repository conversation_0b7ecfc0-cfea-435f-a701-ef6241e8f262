@use "sass:math";
// 默认设计稿的宽度
$designWidth: 1920;
// 默认设计稿的高度
$designHeight: 1080;

// px 转为 vw 的函数
@function vw($px) {
  @return math.div($px, $designWidth) * 100vw;
}

// px 转为 vh 的函数
@function vh($px) {
  @return math.div($px, $designHeight) * 100vh;
}

header {
  position: relative;
  top: 0;
  width: 100%;
  height: vh(60);
  line-height: vh(60);
  display: flex;
  justify-content: space-between;
  align-content: center;
  font-size: vh(18);
  padding: 0 10px;

  .left {
    color: #fff;

    .icon {
      background-image: url("@/assets/images/dark/headerIcon.png");
      margin-right: vw(5);
      height: vh(15);
      width: vh(15);
      background-size: cover;
      background-position: center;
      display: inline-block;
    }
  }

  .right {
    width: 60%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: vw(50);
  }
}
.el-select-dropdown .el-select-dropdown__item {
  padding-left: 1em;
  font-size: vh(16);
}
.el-table .cell {
  font-size: vh(16);
}

::v-deep
  .cfbgc-pagination-options-size-changer.cfbgc-select
  .cfbgc-select-selection--single {
  height: vh(30);
}

::v-deep .cfbgc-table-header {
  overflow: hidden !important;
}

::v-deep .cfbgc-table-body {
  overflow: auto !important;
}

::v-deep .cfbgc-pagination-total-text {
  position: absolute;
  left: 10px;
  color: #fefefe;
}
::v-deep .el-table .el-table__cell {
  padding: 10px 0;
}

[data-theme="defaule"],
[data-theme="tint"] {
  .headerTitle {
    height: 40px;
    line-height: 40px;
    font-size: 16px;
    margin-bottom: 10px;
    background: linear-gradient(
      180deg,
      #f4f8fa,
      #e0e9f1 24%,
      #d5e0eb 61%,
      #d5e0eb
    );
  }
  .normalText {
    color: #0c0d15;
  }
  header .left {
    color: #243963;

    .icon {
      background-image: url("@/assets/images/tint/headerIcon.png");
    }
  }
  .dmis-main {
    ::v-deep .el-table {
      tr:nth-of-type(odd) {
        background-color: #eaeff6;
      }

      tr:nth-of-type(even) {
        background-color: #ffffff;
      }
      .el-table__row:hover {
        td:nth-child(1) {
          border-radius: 10px 0 0 10px;
        }
      }
    }
  }
}

// 库存物资查询样式重写
.search_block {
  align-items: center;
  background-image: none !important;

  .formItem_block {
    display: flex;
    align-items: center;
  }
}

.search_block::before {
  content: "";
  background-image: url("@/assets/images/dark/headerIcon.png");
  margin-right: vw(5);
  height: vh(15);
  width: vh(15);
  background-size: cover;
  background-position: center;
  display: inline-block;
}
.clearBefor::before {
  content: none;
}
::v-deep .el-radio-button.is-active {
  background-color: #5b9bf8;
}

.materialSearch .el-radio-button.is-active {
  background-color: transparent !important;
}

::v-deep .el-radio-group {
  .el-radio-button:nth-child(1) .el-radio-button__inner {
    border-radius: 7px 0 0 7px;
  }

  .el-radio-button:nth-last-child(1) .el-radio-button__inner {
    border-radius: 0 7px 7px 0;
  }

  .el-radio-button--mini .el-radio-button__inner {
    border-color: #4ea0fd;
  }
}

.box {
  border-radius: 10px;
  overflow: hidden;

  .subTitle {
    width: 100%;
    display: flex;
    position: relative;
    justify-content: space-between;
    align-items: center;
    height: vh(45);
    line-height: vh(45);
    background: linear-gradient(270deg, #ffffff, #e4f0ff);

    p {
      width: 100%;
      font-weight: 700;
      font-size: vh(24);
      display: flex;
      align-items: center;
      color: #243963;
      background-image: url("@/assets/images/tint/cardIcon.png");
      background-repeat: no-repeat;
      background-position: vw(18) center;
      padding-left: vw(50);

      img {
        width: vh(20);
        height: vh(20);
        vertical-align: middle;
        margin: 0 vh(10);
        cursor: none;
      }
    }
  }
}

.materialSearch {
  .echart_block {
    margin: 0.5rem 10px;

    .echart_block_item .echart_bg {
      border: none;
      border-radius: 0;
      background-color: #fefefe;
    }
  }

  .content_table_block {
    margin-top: 0.5rem;
  }
}

html[data-theme="dark"] {
  .headerTitle {
    background: url("@/assets/images/header-bg.png") no-repeat !important;
    height: 40px;
    line-height: 40px;
    font-size: 16px;
    background-size: 100% 100%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 10px;
  }
  .normalText {
    color: #fff;
  }
  .dmis-main {
    background-color: #0c1324;

    // el组件样式重写
    // 输入框、下拉框
    ::v-deep .el-input__inner {
      border-radius: vh(7);
      border: none;
      height: vh(30);
      line-height: vh(30);
    }

    // 表格样式
    ::v-deep .el-table__body-wrapper {
      background-color: #0c1324;

      .el-table__empty-text {
        color: white;
      }
    }

    ::v-deep .el-table {
      background-color: #0c1324 !important;

      th.el-table__cell.is-leaf {
        border: none;
      }

      th.el-table__cell {
        background-color: #1b2242;
        color: #cce4ff;
      }

      td.el-table__cell {
        border: none;
        padding: vh(10.5) vh(4);
      }

      tr:nth-of-type(odd) {
        background-color: #24325d;
      }

      tr:nth-of-type(even) {
        background-color: #1b2242;
      }

      .el-table__body tr:hover > td {
        background: #4ea0fc !important;
        color: #fff !important;
      }

      .el-table__row:hover {
        td:nth-child(1) {
          border-radius: 10px 0 0 10px;
        }
      }
    }

    ::v-deep .el-table::before {
      display: none;
    }

    ::v-deep .el-pagination {
      margin-top: vh(15);
    }

    // 多选

    ::v-deep .el-radio-button--mini .el-radio-button__inner {
      background-color: #0c0d15;
      border-color: #2665a8;
    }

    ::v-deep .is-active .el-radio-button__inner {
      background-color: #409eff;
      border: none;
    }
  }

  .subTitle {
    background: linear-gradient(270deg, #1c2546, #243963);

    p {
      color: white;
      background-image: url("@/assets/images/dark/cardIcon.png");
    }
  }

  .weekly .top .search_block .top_title_text {
    color: white;
  }

  .materialSearch {
    .top .search_block .top_title_text {
      color: white;
    }

    .el-radio-button.is-active {
      background-color: transparent !important;
    }

    .echart_block .echart_bg {
      background: none;
      background-color: #1b2242;
    }
  }

  // 时间选择器
  .el-picker-panel {
    border: none;
    .el-date-picker__header-label {
      color: #fff;
    }
    .el-picker-panel__icon-btn {
      color: #6493d4;
    }
    .el-date-table th {
      border-bottom: solid 1px #3b466a;
    }
  }
  // 下拉框
  .el-select-dropdown {
    border: none;

    .el-select-dropdown__item:hover {
      background-color: #3d4a6f;
    }
  }

  .el-select-dropdown.is-multiple .el-select-dropdown__item.selected {
    background-color: #41496f;
  }
}

// ::v-deep html[data-theme="dark"] .el-input__inner {
//     border: none;
// }
