<template>
  <div class="buttonBox">
    <div
      class="buttonItem"
      :class="{ active: activeIndex === index }"
      @click="clickHandle(index)"
      v-for="(item, index) in buttons"
      :key="index"
    >
      {{ item }}
    </div>
  </div>
</template>
<script>
export default {
  name: "buttonBox",
  props: {
    buttons: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      activeIndex: 0,
    };
  },
  methods: {
    clickHandle(index) {
      this.activeIndex = index;
      this.$emit("clickHandle", index);
    },
  },
};
</script>
<style lang="scss" scoped>
.buttonBox {
  margin-top: 12px;
  display: flex;
  .buttonItem {
    margin-right: 8px;
    width: 128px;
    height: 35px;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 16px;
    font-weight: 400;
  }
}
.buttonItem:hover {
  color: #fff !important;
}

[data-theme="dark"] .buttonItem {
  background-image: url("@/assets/tableicon/button-darkbg.png");
  color: #6ba4f4;
}

[data-theme="dark"] .active {
  background-image: url("@/assets/tableicon/buttonactive-darkbg.png") !important;
  color: #fff !important;
}

[data-theme="tint"] .buttonItem {
  background-image: url("@/assets/tableicon/button-tintbg.png");
}

[data-theme="tint"] .active {
  background-image: url("@/assets/tableicon/buttonactive-tintbg.png") !important;
  color: #fff !important;
}
</style>
