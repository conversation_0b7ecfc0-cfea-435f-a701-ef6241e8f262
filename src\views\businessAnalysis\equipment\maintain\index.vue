<template>
  <div class="maintain">
    <div :class="['maintain-title', greenColor ? 'greenColor' : 'buleColor']">
      <div class="maintain-icon"></div>
      <p class="maintain-text">{{ titleContent.text }}</p>
      <p>
        <span class="maintain-amount">2063.34</span>
        <span>万元</span>
      </p>
    </div>
    <div class="maintain-content">
      <pieChartVue></pieChartVue>
    </div>
  </div>
</template>
<script>
import pieChartVue from "@/components/charts/pie/pieChart.vue";
export default {
  props: {
    titleContent: {
      type: Object,
      default: () => {},
    },
    greenColor: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    pieChartVue,
  },
};
</script>
<style lang="scss" scoped>
.maintain {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding-left: 16px;
  padding-top: 16px;

  .maintain-title {
    width: 308px;
    height: 40px;
    border-radius: 4px;
    box-sizing: border-box;
    font-family: Source Han Sans;
    font-size: 14px;
    font-weight: normal;
    display: flex;
    align-items: center;
    .maintain-icon {
      width: 40px;
      height: 40px;
      background-size: 100% 100%;
    }
    .maintain-text {
      margin-left: 12px;
      margin-right: 42px;
    }
    .maintain-amount {
      font-family: Source Han Sans;
      font-size: 22px;
      font-weight: bold;
      margin-right: 4px;
    }
  }

  .maintain-content {
    height: 100%;
  }
}

[data-theme="tint"] .buleColor {
  background: #ecf7ff;
  color: rgba(0, 0, 0, 0.85);
  border: 1px solid rgba(23, 131, 255, 0.5);
  .maintain-icon {
    background-image: url("@/assets/tableicon/maintain-tinticon.png");
  }
}

[data-theme="tint"] .greenColor {
  color: rgba(0, 0, 0, 0.85);
  border: 1px solid rgba(37, 203, 151, 0.5);
  background: #153144;
  .maintain-icon {
    background-image: url("@/assets/tableicon/gbudget-tinticon.png");
  }
}

[data-theme="dark"] .buleColor {
  background: #1a2e5e;
  color: #fff;
  border: 1px solid rgba(23, 131, 255, 0.5);
  .maintain-icon {
    background-image: url("@/assets/tableicon/maintain-darkicon.png");
  }
}

[data-theme="dark"] .greenColor {
  color: #fff;
  border: 1px solid rgba(37, 203, 151, 0.5);
  background: #153144;
  .maintain-icon {
    background-image: url("@/assets/tableicon/gbudget-darkicon.png");
  }
}
</style>
