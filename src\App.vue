<template>
  <div id="app">
    <CfbgcLayout :personalized="personalized">
      <keep-alive>
        <router-view class="dmis-main"></router-view>
      </keep-alive>
    </CfbgcLayout>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import CfbgcMenuDrag from "@/components/cfbgc-menu-drag/index.vue";
const CfbgcLayout = () => import("@/components/cfbgc-layout/index.vue");

@Component({
  components: { CfbgcLayout, CfbgcMenuDrag },
  watch: {
    $route: "checkRoute",
  },
})
export default class App extends Vue {
  public testdata = "";
  public menuOpen: boolean = false;
  public personalized: string | undefined;

  created() {
    this.testdata = process.env;
    this.checkRoute();
  }

  menuOpenChange(data: boolean) {
    this.menuOpen = data;
  }
  checkRoute() {
    this.personalized = (this.$route.meta as any).personalized;
  }
}
</script>

<style lang="scss">
@import "~cfbgc-design/dist/cfbgc-ui";
#app {
  font-family: "Avenir", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;

  .log-menu {
    position: absolute;
    width: $__menuMenuWidth;
    height: 100%;
    left: -10rem;
    background: #2b4e7c;
    cursor: pointer;
    overflow-y: auto;
    overflow-x: hidden;
    z-index: 6;
    transition: all 0.3s;

    &.active {
      left: 0rem;
    }
  }

  .dmis-main {
    width: 100%;
    height: 100%;
    overflow: auto;
  }
}
</style>
